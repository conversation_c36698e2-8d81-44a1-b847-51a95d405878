# MongoDB用户登录功能实现说明

## 概述
基于您提供的MongoDB用户表数据，我已经在SysUserController中实现了一个新的登录接口，支持根据`yongHuZhangHu`（用户账户）和`miMa`（密码）以及验证码进行登录，生成token返回给前端。

## 实现的功能

### 1. 新增的文件

#### 实体类
- `MongoUser.java` - MongoDB用户实体类，映射到表名`444d5fec7d57c38f8e270f028acfca9748d469`

#### 服务类
- `MongoUserService.java` - MongoDB用户服务接口
- `MongoUserServiceImpl.java` - MongoDB用户服务实现类
- `CaptchaService.java` - 验证码服务接口

#### 控制器
- `CaptchaController.java` - 验证码控制器，提供验证码获取接口

### 2. 修改的文件

#### DTO类
- `SysUserLoginReqDTO.java` - 添加了`captcha`字段用于验证码

#### 控制器
- `SysUserController.java` - 添加了新的`/mongo-login`接口

## API接口说明

### 1. MongoDB用户登录接口

**接口地址：** `POST /exam/api/sys/user/mongo-login`

**请求参数：**
```json
{
    "username": "ceshirenzhangsan",  // 用户账户(yongHuZhangHu)
    "password": "wljt123",           // 密码(miMa)
    "captcha": "1234"                // 验证码(可选)
}
```

**响应参数：**
```json
{
    "code": 0,
    "msg": "操作成功",
    "data": {
        "id": "1957325042393612290",
        "userName": "ceshirenzhangsan",
        "realName": "测试人张三",
        "departId": "915c257cab93050227264371d2794b26",
        "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "roles": ["user"],
        "state": 1,
        "createTime": "2025-08-18T14:13:50.000+00:00",
        "updateTime": "2025-08-18T14:13:50.000+00:00"
    }
}
```

### 2. 获取验证码接口

**接口地址：** `GET /exam/api/captcha/get`

**响应参数：**
```json
{
    "code": 0,
    "msg": "操作成功",
    "data": {
        "sessionId": "session-id",
        "captchaImage": "data:image/png;base64,iVBORw0KGgo...",
        "message": "验证码服务未配置，测试时可使用验证码：1234"
    }
}
```

## 功能特点

### 1. 安全性
- 支持验证码验证（可选）
- 密码验证
- 用户状态检查（删除标志检查）
- JWT Token生成

### 2. 兼容性
- 保持与原有登录接口的兼容性
- 使用相同的响应格式
- 复用现有的JWT工具类

### 3. 扩展性
- 验证码服务接口化，便于后续扩展
- 支持不同的验证码实现
- 密码验证逻辑可配置

## 使用说明

### 1. 测试登录
使用您提供的测试数据：
- 用户账户：`ceshirenzhangsan`
- 密码：`wljt123`
- 验证码：`1234`（测试用）

### 2. 验证码配置
当前验证码使用简单的固定值`1234`进行验证，生产环境中需要：
1. 实现完整的`CaptchaService`
2. 集成图形验证码生成库
3. 使用Redis或Session存储验证码

### 3. 密码安全
当前假设密码是明文存储，如果实际是加密存储，需要在`MongoUserServiceImpl.verifyPassword`方法中实现相应的解密或哈希验证逻辑。

## 注意事项

1. **数据库连接**：确保MongoDB连接配置正确
2. **集合名称**：使用的集合名称为`444d5fec7d57c38f8e270f028acfca9748d469`
3. **字段映射**：实体类字段与MongoDB文档字段保持一致
4. **验证码**：当前为简化实现，生产环境需要完善验证码机制
5. **角色权限**：当前设置默认角色为`user`，可根据实际需求调整

## 后续优化建议

1. 实现完整的验证码服务
2. 添加登录失败次数限制
3. 实现密码加密存储和验证
4. 添加用户角色权限管理
5. 实现登录日志记录
6. 添加单点登录支持
