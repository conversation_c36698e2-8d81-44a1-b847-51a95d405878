2025-08-18 17:34:35.813    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.joda.time.LocalDateTime to class java.time.LocalDateTime as writing converter
2025-08-18 17:34:35.813    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.springframework.data.geo.Box to class org.bson.Document as writing converter
2025-08-18 17:34:35.813    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.springframework.data.geo.Polygon to class org.bson.Document as writing converter
2025-08-18 17:34:35.813    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.springframework.data.geo.Circle to class org.bson.Document as writing converter
2025-08-18 17:34:35.813    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.springframework.data.mongodb.core.geo.Sphere to class org.bson.Document as writing converter
2025-08-18 17:34:35.813    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.bson.Document to class org.springframework.data.geo.Box as reading converter
2025-08-18 17:34:35.813    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.bson.Document to class org.springframework.data.geo.Polygon as reading converter
2025-08-18 17:34:35.813    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.bson.Document to class org.springframework.data.geo.Circle as reading converter
2025-08-18 17:34:35.813    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.bson.Document to class org.springframework.data.mongodb.core.geo.Sphere as reading converter
2025-08-18 17:34:35.814    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.bson.Document to class org.springframework.data.geo.Point as reading converter
2025-08-18 17:34:35.814    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.springframework.data.geo.Point to class org.bson.Document as writing converter
2025-08-18 17:34:35.814    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.springframework.data.mongodb.core.query.GeoCommand to class org.bson.Document as writing converter
2025-08-18 17:34:35.814    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from interface org.springframework.data.mongodb.core.geo.GeoJson to class org.bson.Document as writing converter
2025-08-18 17:34:35.814    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.springframework.data.mongodb.core.geo.GeoJsonPoint to class org.bson.Document as writing converter
2025-08-18 17:34:35.814    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.springframework.data.mongodb.core.geo.GeoJsonPolygon to class org.bson.Document as writing converter
2025-08-18 17:34:35.815    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.bson.Document to class org.springframework.data.mongodb.core.geo.GeoJsonPoint as reading converter
2025-08-18 17:34:35.815    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.bson.Document to class org.springframework.data.mongodb.core.geo.GeoJsonPolygon as reading converter
2025-08-18 17:34:35.815    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.bson.Document to class org.springframework.data.mongodb.core.geo.GeoJsonLineString as reading converter
2025-08-18 17:34:35.815    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.bson.Document to class org.springframework.data.mongodb.core.geo.GeoJsonMultiLineString as reading converter
2025-08-18 17:34:35.815    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.bson.Document to class org.springframework.data.mongodb.core.geo.GeoJsonMultiPoint as reading converter
2025-08-18 17:34:35.815    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.bson.Document to class org.springframework.data.mongodb.core.geo.GeoJsonMultiPolygon as reading converter
2025-08-18 17:34:35.815    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.bson.Document to class org.springframework.data.mongodb.core.geo.GeoJsonGeometryCollection as reading converter
2025-08-18 17:34:35.815    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.bson.Document to interface org.springframework.data.mongodb.core.geo.GeoJson as reading converter
2025-08-18 17:34:35.815    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.joda.time.LocalDate to class java.util.Date as writing converter
2025-08-18 17:34:35.815    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.joda.time.LocalDateTime to class java.util.Date as writing converter
2025-08-18 17:34:35.815    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.joda.time.DateTime to class java.util.Date as writing converter
2025-08-18 17:34:35.815    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class org.joda.time.LocalDate as reading converter
2025-08-18 17:34:35.815    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class org.joda.time.LocalDateTime as reading converter
2025-08-18 17:34:35.815    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class org.joda.time.DateTime as reading converter
2025-08-18 17:34:35.815    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Skipping converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter class java.time.LocalDateTime is not a store supported simple type
2025-08-18 17:34:35.815    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Skipping converter from class java.time.LocalDateTime to class org.joda.time.DateTime as writing converter class org.joda.time.DateTime is not a store supported simple type
2025-08-18 17:34:35.815    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Skipping converter from class java.time.Instant to class org.joda.time.LocalDateTime as writing converter class org.joda.time.LocalDateTime is not a store supported simple type
2025-08-18 17:34:35.815    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Skipping converter from class org.joda.time.LocalDateTime to class java.time.Instant as writing converter class java.time.Instant is not a store supported simple type
2025-08-18 17:34:35.815    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Skipping converter from class org.joda.time.LocalDateTime to class java.time.LocalDateTime as writing converter class java.time.LocalDateTime is not a store supported simple type
2025-08-18 17:34:35.815    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class java.time.LocalDateTime as reading converter
2025-08-18 17:34:35.815    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDateTime to class java.util.Date as writing converter
2025-08-18 17:34:35.815    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class java.time.LocalDate as reading converter
2025-08-18 17:34:35.815    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDate to class java.util.Date as writing converter
2025-08-18 17:34:35.815    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class java.time.LocalTime as reading converter
2025-08-18 17:34:35.815    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalTime to class java.util.Date as writing converter
2025-08-18 17:34:35.815    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class java.time.Instant as reading converter
2025-08-18 17:34:35.815    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Instant to class java.util.Date as writing converter
2025-08-18 17:34:35.815    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Skipping converter from class java.time.LocalDateTime to class java.time.Instant as reading converter class java.time.LocalDateTime is not a store supported simple type
2025-08-18 17:34:35.815    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Skipping converter from class java.time.Instant to class java.time.LocalDateTime as reading converter class java.time.Instant is not a store supported simple type
2025-08-18 17:34:35.815    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.ZoneId to class java.lang.String as writing converter
2025-08-18 17:34:35.815    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.ZoneId as reading converter
2025-08-18 17:34:35.815    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Duration to class java.lang.String as writing converter
2025-08-18 17:34:35.815    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.Duration as reading converter
2025-08-18 17:34:35.815    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Period to class java.lang.String as writing converter
2025-08-18 17:34:35.815    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.Period as reading converter
2025-08-18 17:34:35.815    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.LocalDate as reading converter
2025-08-18 17:34:35.816    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.LocalDateTime as reading converter
2025-08-18 17:34:35.816    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.Instant as reading converter
2025-08-18 17:34:35.822    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'mongoMappingContext' via factory method to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@4e6f2bb5'
2025-08-18 17:34:35.822    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'mongoMappingContext' via factory method to bean named 'spring.data.mongodb-org.springframework.boot.autoconfigure.mongo.MongoProperties'
2025-08-18 17:34:35.822    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'mongoMappingContext' via factory method to bean named 'mongoCustomConversions'
2025-08-18 17:34:35.932    [main] DEBUG logger_name:o.s.c.a.ClassPathScanningCandidateComponentProvider - message:Identified candidate component class: file [D:\my\jvs-car\car-biz\target\classes\cn\bctools\modules\datav\dto\CarYearDto.class]
2025-08-18 17:34:35.932    [main] DEBUG logger_name:o.s.c.a.ClassPathScanningCandidateComponentProvider - message:Identified candidate component class: file [D:\my\jvs-car\car-biz\target\classes\cn\bctools\modules\datav\dto\DriverDepartDto.class]
2025-08-18 17:34:35.932    [main] DEBUG logger_name:o.s.c.a.ClassPathScanningCandidateComponentProvider - message:Identified candidate component class: file [D:\my\jvs-car\car-biz\target\classes\cn\bctools\modules\datav\dto\DriverInfoDto.class]
2025-08-18 17:34:35.932    [main] DEBUG logger_name:o.s.c.a.ClassPathScanningCandidateComponentProvider - message:Identified candidate component class: file [D:\my\jvs-car\car-biz\target\classes\cn\bctools\modules\datav\dto\DriverQuaRreviewDto.class]
2025-08-18 17:34:35.932    [main] DEBUG logger_name:o.s.c.a.ClassPathScanningCandidateComponentProvider - message:Identified candidate component class: file [D:\my\jvs-car\car-biz\target\classes\cn\bctools\modules\datav\dto\HiddenDto.class]
2025-08-18 17:34:35.932    [main] DEBUG logger_name:o.s.c.a.ClassPathScanningCandidateComponentProvider - message:Identified candidate component class: file [D:\my\jvs-car\car-biz\target\classes\cn\bctools\modules\datav\dto\HonorDto.class]
2025-08-18 17:34:35.932    [main] DEBUG logger_name:o.s.c.a.ClassPathScanningCandidateComponentProvider - message:Identified candidate component class: file [D:\my\jvs-car\car-biz\target\classes\cn\bctools\modules\datav\dto\MoneyDto.class]
2025-08-18 17:34:35.932    [main] DEBUG logger_name:o.s.c.a.ClassPathScanningCandidateComponentProvider - message:Identified candidate component class: file [D:\my\jvs-car\car-biz\target\classes\cn\bctools\modules\datav\dto\WagesResp.class]
2025-08-18 17:34:35.932    [main] DEBUG logger_name:o.s.c.a.ClassPathScanningCandidateComponentProvider - message:Identified candidate component class: file [D:\my\jvs-car\car-biz\target\classes\cn\bctools\modules\datav\dto\WagesVo.class]
2025-08-18 17:34:35.932    [main] DEBUG logger_name:o.s.c.a.ClassPathScanningCandidateComponentProvider - message:Identified candidate component class: file [D:\my\jvs-car\car-biz\target\classes\cn\bctools\modules\datav\dto\banan\AccidentBaNanDto.class]
2025-08-18 17:34:35.932    [main] DEBUG logger_name:o.s.c.a.ClassPathScanningCandidateComponentProvider - message:Identified candidate component class: file [D:\my\jvs-car\car-biz\target\classes\cn\bctools\modules\datav\dto\banan\ComplaintBaNanDto.class]
2025-08-18 17:34:35.932    [main] DEBUG logger_name:o.s.c.a.ClassPathScanningCandidateComponentProvider - message:Identified candidate component class: file [D:\my\jvs-car\car-biz\target\classes\cn\bctools\modules\datav\dto\banan\HiddenDtoBaNanNew.class]
2025-08-18 17:34:35.932    [main] DEBUG logger_name:o.s.c.a.ClassPathScanningCandidateComponentProvider - message:Identified candidate component class: file [D:\my\jvs-car\car-biz\target\classes\cn\bctools\modules\datav\dto\banan\SummaryBaNanDto.class]
2025-08-18 17:34:35.932    [main] DEBUG logger_name:o.s.c.a.ClassPathScanningCandidateComponentProvider - message:Identified candidate component class: file [D:\my\jvs-car\car-biz\target\classes\cn\bctools\modules\datav\dto\beibei\AccidentBeiBeiDto.class]
2025-08-18 17:34:35.932    [main] DEBUG logger_name:o.s.c.a.ClassPathScanningCandidateComponentProvider - message:Identified candidate component class: file [D:\my\jvs-car\car-biz\target\classes\cn\bctools\modules\datav\dto\beibei\AccidentMingXinDto.class]
2025-08-18 17:34:35.932    [main] DEBUG logger_name:o.s.c.a.ClassPathScanningCandidateComponentProvider - message:Identified candidate component class: file [D:\my\jvs-car\car-biz\target\classes\cn\bctools\modules\datav\dto\beibei\ComplaintBeibeiDto.class]
2025-08-18 17:34:35.932    [main] DEBUG logger_name:o.s.c.a.ClassPathScanningCandidateComponentProvider - message:Identified candidate component class: file [D:\my\jvs-car\car-biz\target\classes\cn\bctools\modules\datav\dto\beibei\ComplaintMingXinDto.class]
2025-08-18 17:34:35.932    [main] DEBUG logger_name:o.s.c.a.ClassPathScanningCandidateComponentProvider - message:Identified candidate component class: file [D:\my\jvs-car\car-biz\target\classes\cn\bctools\modules\datav\dto\beibei\HiddenDtoBeiBeiNew.class]
2025-08-18 17:34:35.932    [main] DEBUG logger_name:o.s.c.a.ClassPathScanningCandidateComponentProvider - message:Identified candidate component class: file [D:\my\jvs-car\car-biz\target\classes\cn\bctools\modules\datav\dto\beibei\HiddenDtoMingXinNew.class]
2025-08-18 17:34:35.932    [main] DEBUG logger_name:o.s.c.a.ClassPathScanningCandidateComponentProvider - message:Identified candidate component class: file [D:\my\jvs-car\car-biz\target\classes\cn\bctools\modules\datav\dto\beibei\SummaryBeiBeiDto.class]
2025-08-18 17:34:35.932    [main] DEBUG logger_name:o.s.c.a.ClassPathScanningCandidateComponentProvider - message:Identified candidate component class: file [D:\my\jvs-car\car-biz\target\classes\cn\bctools\modules\datav\dto\chuzuche\AccidentChuZuCheDto.class]
2025-08-18 17:34:35.932    [main] DEBUG logger_name:o.s.c.a.ClassPathScanningCandidateComponentProvider - message:Identified candidate component class: file [D:\my\jvs-car\car-biz\target\classes\cn\bctools\modules\datav\dto\chuzuche\ComplaintChuZuCheDto.class]
2025-08-18 17:34:35.932    [main] DEBUG logger_name:o.s.c.a.ClassPathScanningCandidateComponentProvider - message:Identified candidate component class: file [D:\my\jvs-car\car-biz\target\classes\cn\bctools\modules\datav\dto\chuzuche\ComplaintChuZuCheOpenDto.class]
2025-08-18 17:34:35.932    [main] DEBUG logger_name:o.s.c.a.ClassPathScanningCandidateComponentProvider - message:Identified candidate component class: file [D:\my\jvs-car\car-biz\target\classes\cn\bctools\modules\datav\dto\chuzuche\HiddenDtoChuZuCheNew.class]
2025-08-18 17:34:35.932    [main] DEBUG logger_name:o.s.c.a.ClassPathScanningCandidateComponentProvider - message:Identified candidate component class: file [D:\my\jvs-car\car-biz\target\classes\cn\bctools\modules\datav\dto\chuzuche\SummaryChuZuCheDto.class]
2025-08-18 17:34:35.932    [main] DEBUG logger_name:o.s.c.a.ClassPathScanningCandidateComponentProvider - message:Identified candidate component class: file [D:\my\jvs-car\car-biz\target\classes\cn\bctools\modules\datav\dto\inspect\MonthInspectDto.class]
2025-08-18 17:34:35.932    [main] DEBUG logger_name:o.s.c.a.ClassPathScanningCandidateComponentProvider - message:Identified candidate component class: file [D:\my\jvs-car\car-biz\target\classes\cn\bctools\modules\datav\dto\inspect\MonthYouInspectDto.class]
2025-08-18 17:34:35.932    [main] DEBUG logger_name:o.s.c.a.ClassPathScanningCandidateComponentProvider - message:Identified candidate component class: file [D:\my\jvs-car\car-biz\target\classes\cn\bctools\modules\datav\dto\inspect\QuarterInspectDto.class]
2025-08-18 17:34:35.932    [main] DEBUG logger_name:o.s.c.a.ClassPathScanningCandidateComponentProvider - message:Identified candidate component class: file [D:\my\jvs-car\car-biz\target\classes\cn\bctools\modules\datav\dto\inspect\TenInspectDto.class]
2025-08-18 17:34:35.932    [main] DEBUG logger_name:o.s.c.a.ClassPathScanningCandidateComponentProvider - message:Identified candidate component class: file [D:\my\jvs-car\car-biz\target\classes\cn\bctools\modules\datav\dto\yubei\AccidentYuBeiDto.class]
2025-08-18 17:34:35.932    [main] DEBUG logger_name:o.s.c.a.ClassPathScanningCandidateComponentProvider - message:Identified candidate component class: file [D:\my\jvs-car\car-biz\target\classes\cn\bctools\modules\datav\dto\yubei\ComplaintYuBeiDto.class]
2025-08-18 17:34:35.932    [main] DEBUG logger_name:o.s.c.a.ClassPathScanningCandidateComponentProvider - message:Identified candidate component class: file [D:\my\jvs-car\car-biz\target\classes\cn\bctools\modules\datav\dto\yubei\DriverInfoDto.class]
2025-08-18 17:34:35.932    [main] DEBUG logger_name:o.s.c.a.ClassPathScanningCandidateComponentProvider - message:Identified candidate component class: file [D:\my\jvs-car\car-biz\target\classes\cn\bctools\modules\datav\dto\yubei\HiddenDtoYuBeiNew.class]
2025-08-18 17:34:35.932    [main] DEBUG logger_name:o.s.c.a.ClassPathScanningCandidateComponentProvider - message:Identified candidate component class: file [D:\my\jvs-car\car-biz\target\classes\cn\bctools\modules\datav\dto\yubei\SummaryYuBeiDto.class]
2025-08-18 17:34:35.933    [main] DEBUG logger_name:o.s.c.a.ClassPathScanningCandidateComponentProvider - message:Identified candidate component class: file [D:\my\jvs-car\car-biz\target\classes\cn\bctools\modules\sys\user\entity\MongoUser.class]
2025-08-18 17:34:36.127    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'springApplicationAdminRegistrar'
2025-08-18 17:34:36.127    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.admin.SpringApplicationAdminJmxAutoConfiguration'
2025-08-18 17:34:36.131    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'springApplicationAdminRegistrar' via factory method to bean named 'environment'
2025-08-18 17:34:36.133    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'mbeanExporter'
2025-08-18 17:34:36.134    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'objectNamingStrategy'
2025-08-18 17:34:36.140    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'mbeanExporter' via factory method to bean named 'objectNamingStrategy'
2025-08-18 17:34:36.140    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'mbeanExporter' via factory method to bean named 'org.springframework.beans.factory.support.DefaultListableBeanFactory@5e230fc6'
2025-08-18 17:34:36.156    [main] DEBUG logger_name:o.s.b.a.SpringApplicationAdminMXBeanRegistrar$SpringApplicationAdmin - message:Application Admin MBean registered with name 'org.springframework.boot:type=Admin,name=SpringApplication'
2025-08-18 17:34:36.170    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'refreshEventListener'
2025-08-18 17:34:36.170    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.autoconfigure.RefreshAutoConfiguration'
2025-08-18 17:34:36.177    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'legacyContextRefresher'
2025-08-18 17:34:36.180    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'spring.cloud.refresh-org.springframework.cloud.autoconfigure.RefreshAutoConfiguration$RefreshProperties'
2025-08-18 17:34:36.183    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'legacyContextRefresher' via factory method to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@4e6f2bb5'
2025-08-18 17:34:36.183    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'legacyContextRefresher' via factory method to bean named 'refreshScope'
2025-08-18 17:34:36.183    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'legacyContextRefresher' via factory method to bean named 'spring.cloud.refresh-org.springframework.cloud.autoconfigure.RefreshAutoConfiguration$RefreshProperties'
2025-08-18 17:34:36.189    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'refreshEventListener' via factory method to bean named 'legacyContextRefresher'
2025-08-18 17:34:36.195    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'sleuthContextListener'
2025-08-18 17:34:36.428    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'mappingMongoConverter' via factory method to bean named 'mongoDatabaseFactory'
2025-08-18 17:34:36.428    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'mappingMongoConverter' via factory method to bean named 'mongoMappingContext'
2025-08-18 17:34:36.428    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'mappingMongoConverter' via factory method to bean named 'mongoCustomConversions'
2025-08-18 17:34:36.454    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Locale to class java.lang.String as writing converter
2025-08-18 17:34:36.454    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.Character to class java.lang.String as writing converter
2025-08-18 17:34:36.454    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.math.BigDecimal to class java.lang.String as writing converter
2025-08-18 17:34:36.455    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.math.BigDecimal to class org.bson.types.Decimal128 as writing converter
2025-08-18 17:34:36.455    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.math.BigDecimal as reading converter
2025-08-18 17:34:36.455    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.bson.types.Decimal128 to class java.math.BigDecimal as reading converter
2025-08-18 17:34:36.455    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.math.BigInteger to class java.lang.String as writing converter
2025-08-18 17:34:36.455    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.math.BigInteger as reading converter
2025-08-18 17:34:36.455    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.net.URL to class java.lang.String as writing converter
2025-08-18 17:34:36.455    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.net.URL as reading converter
2025-08-18 17:34:36.455    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.bson.Document to class java.lang.String as reading converter
2025-08-18 17:34:36.455    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.springframework.data.mongodb.core.query.Term to class java.lang.String as writing converter
2025-08-18 17:34:36.455    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.springframework.data.mongodb.core.script.NamedMongoScript to class org.bson.Document as writing converter
2025-08-18 17:34:36.455    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.bson.Document to class org.springframework.data.mongodb.core.script.NamedMongoScript as reading converter
2025-08-18 17:34:36.455    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Currency to class java.lang.String as writing converter
2025-08-18 17:34:36.455    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.util.Currency as reading converter
2025-08-18 17:34:36.455    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.concurrent.atomic.AtomicInteger to class java.lang.Integer as writing converter
2025-08-18 17:34:36.455    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.concurrent.atomic.AtomicLong to class java.lang.Long as writing converter
2025-08-18 17:34:36.455    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.Long to class java.util.concurrent.atomic.AtomicLong as reading converter
2025-08-18 17:34:36.455    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.Integer to class java.util.concurrent.atomic.AtomicInteger as reading converter
2025-08-18 17:34:36.455    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.bson.types.Binary to class [B as reading converter
2025-08-18 17:34:36.455    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.bson.BsonTimestamp to class java.time.Instant as reading converter
2025-08-18 17:34:36.455    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.net.URI as reading converter
2025-08-18 17:34:36.455    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.net.URI to class java.lang.String as writing converter
2025-08-18 17:34:36.455    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.joda.time.LocalDate to class java.util.Date as writing converter
2025-08-18 17:34:36.455    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.joda.time.LocalDateTime to class java.util.Date as writing converter
2025-08-18 17:34:36.455    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.joda.time.DateTime to class java.util.Date as writing converter
2025-08-18 17:34:36.455    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class org.joda.time.LocalDate as reading converter
2025-08-18 17:34:36.455    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class org.joda.time.LocalDateTime as reading converter
2025-08-18 17:34:36.455    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class org.joda.time.DateTime as reading converter
2025-08-18 17:34:36.455    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter
2025-08-18 17:34:36.455    [main] WARN  logger_name:o.s.data.convert.CustomConversions - message:Registering converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter although it doesn't convert from a store-supported type; You might want to check your annotation setup at the converter implementation
2025-08-18 17:34:36.455    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDateTime to class org.joda.time.DateTime as writing converter
2025-08-18 17:34:36.455    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Instant to class org.joda.time.LocalDateTime as writing converter
2025-08-18 17:34:36.455    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.joda.time.LocalDateTime to class java.time.Instant as writing converter
2025-08-18 17:34:36.455    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.joda.time.LocalDateTime to class java.time.LocalDateTime as writing converter
2025-08-18 17:34:36.455    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.springframework.data.geo.Box to class org.bson.Document as writing converter
2025-08-18 17:34:36.455    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.springframework.data.geo.Polygon to class org.bson.Document as writing converter
2025-08-18 17:34:36.455    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.springframework.data.geo.Circle to class org.bson.Document as writing converter
2025-08-18 17:34:36.455    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.springframework.data.mongodb.core.geo.Sphere to class org.bson.Document as writing converter
2025-08-18 17:34:36.455    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.bson.Document to class org.springframework.data.geo.Box as reading converter
2025-08-18 17:34:36.456    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.bson.Document to class org.springframework.data.geo.Polygon as reading converter
2025-08-18 17:34:36.456    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.bson.Document to class org.springframework.data.geo.Circle as reading converter
2025-08-18 17:34:36.456    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.bson.Document to class org.springframework.data.mongodb.core.geo.Sphere as reading converter
2025-08-18 17:34:36.456    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.bson.Document to class org.springframework.data.geo.Point as reading converter
2025-08-18 17:34:36.456    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.springframework.data.geo.Point to class org.bson.Document as writing converter
2025-08-18 17:34:36.456    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.springframework.data.mongodb.core.query.GeoCommand to class org.bson.Document as writing converter
2025-08-18 17:34:36.456    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from interface org.springframework.data.mongodb.core.geo.GeoJson to class org.bson.Document as writing converter
2025-08-18 17:34:36.456    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.springframework.data.mongodb.core.geo.GeoJsonPoint to class org.bson.Document as writing converter
2025-08-18 17:34:36.456    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.springframework.data.mongodb.core.geo.GeoJsonPolygon to class org.bson.Document as writing converter
2025-08-18 17:34:36.456    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.bson.Document to class org.springframework.data.mongodb.core.geo.GeoJsonPoint as reading converter
2025-08-18 17:34:36.456    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.bson.Document to class org.springframework.data.mongodb.core.geo.GeoJsonPolygon as reading converter
2025-08-18 17:34:36.456    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.bson.Document to class org.springframework.data.mongodb.core.geo.GeoJsonLineString as reading converter
2025-08-18 17:34:36.456    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.bson.Document to class org.springframework.data.mongodb.core.geo.GeoJsonMultiLineString as reading converter
2025-08-18 17:34:36.456    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.bson.Document to class org.springframework.data.mongodb.core.geo.GeoJsonMultiPoint as reading converter
2025-08-18 17:34:36.456    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.bson.Document to class org.springframework.data.mongodb.core.geo.GeoJsonMultiPolygon as reading converter
2025-08-18 17:34:36.456    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.bson.Document to class org.springframework.data.mongodb.core.geo.GeoJsonGeometryCollection as reading converter
2025-08-18 17:34:36.456    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.bson.Document to interface org.springframework.data.mongodb.core.geo.GeoJson as reading converter
2025-08-18 17:34:36.456    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.joda.time.LocalDate to class java.util.Date as writing converter
2025-08-18 17:34:36.456    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.joda.time.LocalDateTime to class java.util.Date as writing converter
2025-08-18 17:34:36.456    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.joda.time.DateTime to class java.util.Date as writing converter
2025-08-18 17:34:36.456    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class org.joda.time.LocalDate as reading converter
2025-08-18 17:34:36.456    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class org.joda.time.LocalDateTime as reading converter
2025-08-18 17:34:36.456    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class org.joda.time.DateTime as reading converter
2025-08-18 17:34:36.456    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Skipping converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter class java.time.LocalDateTime is not a store supported simple type
2025-08-18 17:34:36.456    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Skipping converter from class java.time.LocalDateTime to class org.joda.time.DateTime as writing converter class org.joda.time.DateTime is not a store supported simple type
2025-08-18 17:34:36.456    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Skipping converter from class java.time.Instant to class org.joda.time.LocalDateTime as writing converter class org.joda.time.LocalDateTime is not a store supported simple type
2025-08-18 17:34:36.456    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Skipping converter from class org.joda.time.LocalDateTime to class java.time.Instant as writing converter class java.time.Instant is not a store supported simple type
2025-08-18 17:34:36.456    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Skipping converter from class org.joda.time.LocalDateTime to class java.time.LocalDateTime as writing converter class java.time.LocalDateTime is not a store supported simple type
2025-08-18 17:34:36.456    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class java.time.LocalDateTime as reading converter
2025-08-18 17:34:36.456    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDateTime to class java.util.Date as writing converter
2025-08-18 17:34:36.456    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class java.time.LocalDate as reading converter
2025-08-18 17:34:36.456    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDate to class java.util.Date as writing converter
2025-08-18 17:34:36.456    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class java.time.LocalTime as reading converter
2025-08-18 17:34:36.456    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalTime to class java.util.Date as writing converter
2025-08-18 17:34:36.456    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class java.time.Instant as reading converter
2025-08-18 17:34:36.456    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Instant to class java.util.Date as writing converter
2025-08-18 17:34:36.456    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Skipping converter from class java.time.LocalDateTime to class java.time.Instant as reading converter class java.time.LocalDateTime is not a store supported simple type
2025-08-18 17:34:36.456    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Skipping converter from class java.time.Instant to class java.time.LocalDateTime as reading converter class java.time.Instant is not a store supported simple type
2025-08-18 17:34:36.456    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.ZoneId to class java.lang.String as writing converter
2025-08-18 17:34:36.456    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.ZoneId as reading converter
2025-08-18 17:34:36.456    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Duration to class java.lang.String as writing converter
2025-08-18 17:34:36.456    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.Duration as reading converter
2025-08-18 17:34:36.456    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Period to class java.lang.String as writing converter
2025-08-18 17:34:36.456    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.Period as reading converter
2025-08-18 17:34:36.456    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.LocalDate as reading converter
2025-08-18 17:34:36.456    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.LocalDateTime as reading converter
2025-08-18 17:34:36.456    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.Instant as reading converter
2025-08-18 17:34:36.561    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'mongoTemplate' via factory method to bean named 'mongoDatabaseFactory'
2025-08-18 17:34:36.561    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'mongoTemplate' via factory method to bean named 'mappingMongoConverter'
2025-08-18 17:34:36.720    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'cn.bctools.auth.api.api.AuthDeptServiceApi'
2025-08-18 17:34:36.721    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'feignContext'
2025-08-18 17:34:36.721    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.openfeign.FeignAutoConfiguration'
2025-08-18 17:34:36.723    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'default.cn.bctools.BizApplication.FeignClientSpecification'
2025-08-18 17:34:36.729    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'rule.FeignClientSpecification'
2025-08-18 17:34:36.731    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'datamodel.FeignClientSpecification'
2025-08-18 17:34:36.732    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'tree.FeignClientSpecification'
2025-08-18 17:34:36.733    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'useapp.FeignClientSpecification'
2025-08-18 17:34:36.734    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'tenantConfig.FeignClientSpecification'
2025-08-18 17:34:36.735    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'dept.FeignClientSpecification'
2025-08-18 17:34:36.736    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'role.FeignClientSpecification'
2025-08-18 17:34:36.738    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'dict.FeignClientSpecification'
2025-08-18 17:34:36.739    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'selected.FeignClientSpecification'
2025-08-18 17:34:36.739    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'userExtension.FeignClientSpecification'
2025-08-18 17:34:36.740    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'tenant.FeignClientSpecification'
2025-08-18 17:34:36.741    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'apply.FeignClientSpecification'
2025-08-18 17:34:36.743    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'file.FeignClientSpecification'
2025-08-18 17:34:36.745    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'user.FeignClientSpecification'
2025-08-18 17:34:36.746    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'userGroup.FeignClientSpecification'
2025-08-18 17:34:36.747    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'job.FeignClientSpecification'
2025-08-18 17:34:36.766    [main] DEBUG logger_name:o.s.c.a.AnnotationConfigApplicationContext - message:Refreshing FeignContext-dept
2025-08-18 17:34:36.766    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.context.annotation.internalConfigurationAnnotationProcessor'
2025-08-18 17:34:36.788    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'propertySourcesPlaceholderConfigurer'
2025-08-18 17:34:36.789    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.context.event.internalEventListenerProcessor'
2025-08-18 17:34:36.789    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.context.event.internalEventListenerFactory'
2025-08-18 17:34:36.789    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.context.annotation.internalAutowiredAnnotationProcessor'
2025-08-18 17:34:36.790    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.context.annotation.internalCommonAnnotationProcessor'
2025-08-18 17:34:36.790    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'propertyPlaceholderAutoConfiguration'
2025-08-18 17:34:36.790    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'feignClientsConfiguration'
2025-08-18 17:34:36.797    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'spring.data.web-org.springframework.boot.autoconfigure.data.web.SpringDataWebProperties'
2025-08-18 17:34:36.803    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'feign.client-org.springframework.cloud.openfeign.FeignClientProperties'
2025-08-18 17:34:36.816    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'feign.encoder-org.springframework.cloud.openfeign.support.FeignEncoderProperties'
2025-08-18 17:34:36.818    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration'
2025-08-18 17:34:36.818    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'feignQueryMapEncoderPageable'
2025-08-18 17:34:36.823    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'feignContract'
2025-08-18 17:34:36.824    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'feignConversionService'
2025-08-18 17:34:36.826    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'feignContract' via factory method to bean named 'feignConversionService'
2025-08-18 17:34:36.846    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'feignRetryer'
2025-08-18 17:34:36.849    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'feignLoggerFactory'
2025-08-18 17:34:36.850    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'feignClientConfigurer'
2025-08-18 17:34:36.852    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'mvcResourceUrlProvider'
2025-08-18 17:34:36.852    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.web.servlet.config.annotation.DelegatingWebMvcConfiguration'
2025-08-18 17:34:36.872    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'corsConfig'
2025-08-18 17:34:36.879    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'cn.bctools.web.config.JvsWebMvcConfigurer'
2025-08-18 17:34:36.880    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'cn.bctools.feign.config.SwaggerProperties'
2025-08-18 17:34:36.900    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.data.web.config.SpringDataWebConfiguration'
2025-08-18 17:34:36.901    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'org.springframework.data.web.config.SpringDataWebConfiguration' via constructor to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@4e6f2bb5'
2025-08-18 17:34:36.913    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebMvcConfigurer'
2025-08-18 17:34:36.920    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'swaggerUiConfigurer'
2025-08-18 17:34:36.920    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'springfox.boot.starter.autoconfigure.SwaggerUiWebMvcConfiguration'
2025-08-18 17:34:36.974    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.amqp.rabbit.config.internalRabbitListenerEndpointRegistry'
2025-08-18 17:34:36.987    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'bindingService'
2025-08-18 17:34:36.988    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'spring.cloud.stream-org.springframework.cloud.stream.config.BindingServiceProperties'
2025-08-18 17:34:36.991    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'integrationConversionService'
2025-08-18 17:34:37.021    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'binderFactory'
2025-08-18 17:34:37.023    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'binderTypeRegistry'
2025-08-18 17:34:37.023    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.stream.config.BinderFactoryAutoConfiguration'
2025-08-18 17:34:37.029    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'binderTypeRegistry' via factory method to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@4e6f2bb5'
2025-08-18 17:34:37.039    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'binderFactory' via factory method to bean named 'binderTypeRegistry'
2025-08-18 17:34:37.039    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'binderFactory' via factory method to bean named 'spring.cloud.stream-org.springframework.cloud.stream.config.BindingServiceProperties'
2025-08-18 17:34:37.054    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'taskScheduler'
2025-08-18 17:34:37.054    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.task.TaskSchedulingAutoConfiguration'
2025-08-18 17:34:37.058    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'taskSchedulerBuilder'
2025-08-18 17:34:37.059    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'spring.task.scheduling-org.springframework.boot.autoconfigure.task.TaskSchedulingProperties'
2025-08-18 17:34:37.064    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'taskSchedulerBuilder' via factory method to bean named 'spring.task.scheduling-org.springframework.boot.autoconfigure.task.TaskSchedulingProperties'
2025-08-18 17:34:37.070    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'taskScheduler' via factory method to bean named 'taskSchedulerBuilder'
2025-08-18 17:34:37.080    [main] DEBUG logger_name:o.s.s.c.ThreadPoolTaskScheduler - message:Initializing ExecutorService 'taskScheduler'
2025-08-18 17:34:37.107    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'spring.sleuth.async-org.springframework.cloud.sleuth.autoconfig.instrument.async.SleuthAsyncProperties'
2025-08-18 17:34:37.117    [main] DEBUG logger_name:o.s.aop.framework.ProxyFactoryBean - message:Advice has changed; re-caching singleton instance
2025-08-18 17:34:37.159    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'jacksonObjectMapper'
2025-08-18 17:34:37.159    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration'
2025-08-18 17:34:37.164    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration'
2025-08-18 17:34:37.167    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'customizer'
2025-08-18 17:34:37.180    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'standardJacksonObjectMapperBuilderCustomizer'
2025-08-18 17:34:37.180    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration'
2025-08-18 17:34:37.183    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties'
2025-08-18 17:34:37.190    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'standardJacksonObjectMapperBuilderCustomizer' via factory method to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@4e6f2bb5'
2025-08-18 17:34:37.190    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'standardJacksonObjectMapperBuilderCustomizer' via factory method to bean named 'spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties'
2025-08-18 17:34:37.197    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'jacksonObjectMapperBuilder' via factory method to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@4e6f2bb5'
2025-08-18 17:34:37.197    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'jacksonObjectMapperBuilder' via factory method to bean named 'customizer'
2025-08-18 17:34:37.197    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'jacksonObjectMapperBuilder' via factory method to bean named 'standardJacksonObjectMapperBuilderCustomizer'
2025-08-18 17:34:37.199    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'parameterNamesModule'
2025-08-18 17:34:37.199    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration'
2025-08-18 17:34:37.221    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'jsonComponentModule'
2025-08-18 17:34:37.221    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration'
2025-08-18 17:34:37.241    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'jsonMixinModule'
2025-08-18 17:34:37.241    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'jsonMixinModule' via factory method to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@4e6f2bb5'
2025-08-18 17:34:37.330    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'geoJsonModule'
2025-08-18 17:34:37.330    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.data.mongodb.config.GeoJsonConfiguration'
2025-08-18 17:34:37.348    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'jacksonGeoModule'
2025-08-18 17:34:37.348    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.data.web.config.SpringDataJacksonConfiguration'
2025-08-18 17:34:37.370    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'jacksonObjectMapper' via factory method to bean named 'jacksonObjectMapperBuilder'
2025-08-18 17:34:37.467    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'bindingService' via factory method to bean named 'spring.cloud.stream-org.springframework.cloud.stream.config.BindingServiceProperties'
2025-08-18 17:34:37.467    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'bindingService' via factory method to bean named 'binderFactory'
2025-08-18 17:34:37.467    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'bindingService' via factory method to bean named 'taskScheduler'
2025-08-18 17:34:37.467    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'bindingService' via factory method to bean named 'jacksonObjectMapper'
2025-08-18 17:34:37.475    [main] DEBUG logger_name:o.h.v.i.x.config.ValidationXmlParser - message:Trying to load META-INF/validation.xml for XML based Validator configuration.
2025-08-18 17:34:37.475    [main] DEBUG logger_name:o.h.v.i.x.c.ResourceLoaderHelper - message:Trying to load META-INF/validation.xml via TCCL
2025-08-18 17:34:37.476    [main] DEBUG logger_name:o.h.v.i.x.c.ResourceLoaderHelper - message:Trying to load META-INF/validation.xml via Hibernate Validator's class loader
2025-08-18 17:34:37.476    [main] DEBUG logger_name:o.h.v.i.x.config.ValidationXmlParser - message:No META-INF/validation.xml found. Using annotation based configuration only.
2025-08-18 17:34:37.479    [main] DEBUG logger_name:o.h.v.i.e.r.TraversableResolvers - message:Cannot find javax.persistence.Persistence on classpath. Assuming non JPA 2 environment. All properties will per default be traversable.
2025-08-18 17:34:37.480    [main] DEBUG logger_name:o.h.v.m.ResourceBundleMessageInterpolator - message:Loaded expression factory via original TCCL
2025-08-18 17:34:37.481    [main] DEBUG logger_name:o.h.v.i.e.ValidatorFactoryConfigurationHelper - message:HV000252: Using org.hibernate.validator.internal.engine.DefaultPropertyNodeNameProvider as property node name provider.
2025-08-18 17:34:37.482    [main] DEBUG logger_name:o.h.v.i.e.ValidatorFactoryConfigurationHelper - message:HV000234: Using org.hibernate.validator.messageinterpolation.ResourceBundleMessageInterpolator as ValidatorFactory-scoped message interpolator.
2025-08-18 17:34:37.482    [main] DEBUG logger_name:o.h.v.i.e.ValidatorFactoryConfigurationHelper - message:HV000234: Using org.hibernate.validator.internal.engine.resolver.TraverseAllTraversableResolver as ValidatorFactory-scoped traversable resolver.
2025-08-18 17:34:37.482    [main] DEBUG logger_name:o.h.v.i.e.ValidatorFactoryConfigurationHelper - message:HV000234: Using org.hibernate.validator.internal.util.ExecutableParameterNameProvider as ValidatorFactory-scoped parameter name provider.
2025-08-18 17:34:37.482    [main] DEBUG logger_name:o.h.v.i.e.ValidatorFactoryConfigurationHelper - message:HV000234: Using org.hibernate.validator.internal.engine.DefaultClockProvider as ValidatorFactory-scoped clock provider.
2025-08-18 17:34:37.482    [main] DEBUG logger_name:o.h.v.i.e.ValidatorFactoryConfigurationHelper - message:HV000234: Using org.hibernate.validator.internal.engine.scripting.DefaultScriptEvaluatorFactory as ValidatorFactory-scoped script evaluator factory.
2025-08-18 17:34:37.502    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'contextStartAfterRefreshListener'
2025-08-18 17:34:37.506    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'appListener'
2025-08-18 17:34:37.507    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'spring.cloud.stream.integration-org.springframework.cloud.stream.config.SpringIntegrationProperties'
2025-08-18 17:34:37.510    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'appListener' via factory method to bean named 'spring.cloud.stream.integration-org.springframework.cloud.stream.config.SpringIntegrationProperties'
2025-08-18 17:34:37.518    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.integration.config.IdGeneratorConfigurer#0'
2025-08-18 17:34:37.524    [main] DEBUG logger_name:o.s.c.e.PropertySourcesPropertyResolver - message:Found key 'spring.liveBeansView.mbeanDomain' in PropertySource 'systemProperties' with value of type String
2025-08-18 17:34:37.530    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.sleuth.autoconfig.instrument.web.client.feign.TraceFeignClientAutoConfiguration$CircuitBreakerMissingConfiguration'
2025-08-18 17:34:37.533    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'feignBuilder' via factory method to bean named 'org.springframework.beans.factory.support.DefaultListableBeanFactory@5e230fc6'
2025-08-18 17:34:37.596    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'feignEncodingConfig'
2025-08-18 17:34:37.641    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'errorDecoder'
2025-08-18 17:34:37.647    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'jvsRequestInterceptor'
2025-08-18 17:34:37.659    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'cachingCapability'
2025-08-18 17:34:37.661    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'cachingCapability' via factory method to bean named 'cacheInterceptor'
2025-08-18 17:34:37.675    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'feignRetryClient'
2025-08-18 17:34:37.675    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.openfeign.loadbalancer.OkHttpFeignLoadBalancerConfiguration'
2025-08-18 17:34:37.680    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'blockingLoadBalancerClient'
2025-08-18 17:34:37.680    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.loadbalancer.config.BlockingLoadBalancerClientAutoConfiguration'
2025-08-18 17:34:37.684    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'loadBalancerClientFactory'
2025-08-18 17:34:37.684    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.loadbalancer.config.LoadBalancerAutoConfiguration'
2025-08-18 17:34:37.688    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'spring.cloud.loadbalancer-org.springframework.cloud.client.loadbalancer.LoadBalancerClientsProperties'
2025-08-18 17:34:37.707    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'loadBalancerClientFactory' via factory method to bean named 'spring.cloud.loadbalancer-org.springframework.cloud.client.loadbalancer.LoadBalancerClientsProperties'
2025-08-18 17:34:37.708    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'default.cn.bctools.gray.config.GrayConfig.LoadBalancerClientSpecification'
2025-08-18 17:34:37.719    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'default.org.springframework.cloud.loadbalancer.config.LoadBalancerAutoConfiguration.LoadBalancerClientSpecification'
2025-08-18 17:34:37.720    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'default.org.springframework.cloud.loadbalancer.config.BlockingLoadBalancerClientAutoConfiguration.LoadBalancerClientSpecification'
2025-08-18 17:34:37.725    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'blockingLoadBalancerClient' via factory method to bean named 'loadBalancerClientFactory'
2025-08-18 17:34:37.739    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'client'
2025-08-18 17:34:37.739    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.openfeign.clientconfig.OkHttpFeignConfiguration'
2025-08-18 17:34:37.743    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'okHttpClientFactory'
2025-08-18 17:34:37.743    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.commons.httpclient.HttpClientConfiguration$OkHttpClientConfiguration'
2025-08-18 17:34:37.748    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'okHttpClientBuilder'
2025-08-18 17:34:38.200    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'okHttpClientFactory' via factory method to bean named 'okHttpClientBuilder'
2025-08-18 17:34:38.206    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'httpClientConnectionPool'
2025-08-18 17:34:38.207    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'feign.httpclient-org.springframework.cloud.openfeign.support.FeignHttpClientProperties'
2025-08-18 17:34:38.217    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'connPoolFactory'
2025-08-18 17:34:38.222    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'httpClientConnectionPool' via factory method to bean named 'feign.httpclient-org.springframework.cloud.openfeign.support.FeignHttpClientProperties'
2025-08-18 17:34:38.222    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'httpClientConnectionPool' via factory method to bean named 'connPoolFactory'
2025-08-18 17:34:38.224    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'client' via factory method to bean named 'okHttpClientFactory'
2025-08-18 17:34:38.224    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'client' via factory method to bean named 'httpClientConnectionPool'
2025-08-18 17:34:38.224    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'client' via factory method to bean named 'feign.httpclient-org.springframework.cloud.openfeign.support.FeignHttpClientProperties'
2025-08-18 17:34:38.303    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'loadBalancedRetryFactory'
2025-08-18 17:34:38.303    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.loadbalancer.config.BlockingLoadBalancerClientAutoConfiguration$BlockingLoadBalancerRetryConfig'
2025-08-18 17:34:38.309    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'loadBalancedRetryFactory' via factory method to bean named 'loadBalancerClientFactory'
2025-08-18 17:34:38.321    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'feignRetryClient' via factory method to bean named 'blockingLoadBalancerClient'
2025-08-18 17:34:38.321    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'feignRetryClient' via factory method to bean named 'client'
2025-08-18 17:34:38.321    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'feignRetryClient' via factory method to bean named 'loadBalancedRetryFactory'
2025-08-18 17:34:38.321    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'feignRetryClient' via factory method to bean named 'loadBalancerClientFactory'
2025-08-18 17:34:38.358    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'feignTargeter'
2025-08-18 17:34:38.358    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.openfeign.FeignAutoConfiguration$DefaultFeignTargeterConfiguration'
2025-08-18 17:34:38.455    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'modelMapper'
2025-08-18 17:34:38.466    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'sqlSessionFactory'
2025-08-18 17:34:38.467    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration'
2025-08-18 17:34:38.470    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties'
2025-08-18 17:34:38.695    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration' via constructor to bean named 'mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties'
2025-08-18 17:34:38.695    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration' via constructor to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@4e6f2bb5'
2025-08-18 17:34:38.696    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'mybatisPlusInterceptor'
2025-08-18 17:34:38.696    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'cn.bctools.database.config.MybatisPlusConfig'
2025-08-18 17:34:38.704    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'otherInterceptor'
2025-08-18 17:34:38.705    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'tenantLineInnerInterceptor'
2025-08-18 17:34:38.705    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'tenantHandler'
2025-08-18 17:34:38.716    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'tableFieldGetter'
2025-08-18 17:34:38.716    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'cn.bctools.database.config.DatabaseConfig'
2025-08-18 17:34:38.719    [main] INFO  logger_name:c.b.database.config.DatabaseConfig - message:[mysql-data] 使用默认的表字段获取类: cn.bctools.database.getter.DefaultTableFieldGetter
2025-08-18 17:34:38.727    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'dataSourceGetter'
2025-08-18 17:34:38.728    [main] INFO  logger_name:c.b.database.config.DatabaseConfig - message:[mysql-data] 使用默认的数据源信息获取类: cn.bctools.database.getter.DefaultDataSourceGetter
2025-08-18 17:34:38.735    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'tenantLineInnerInterceptor' via factory method to bean named 'tenantHandler'
2025-08-18 17:34:38.765    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'dynamicTableNameInnerInterceptor'
2025-08-18 17:34:38.773    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'otherInterceptor' via factory method to bean named 'tenantLineInnerInterceptor'
2025-08-18 17:34:38.773    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'otherInterceptor' via factory method to bean named 'dynamicTableNameInnerInterceptor'
2025-08-18 17:34:38.778    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'mybatisPlusInterceptor' via factory method to bean named 'otherInterceptor'
2025-08-18 17:34:38.805    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'databaseIdProvider'
2025-08-18 17:34:38.817    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'sqlSessionFactory' via factory method to bean named 'dataSource'
2025-08-18 17:34:38.838    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'cn.bctools.database.config.AutoFillConfig'
2025-08-18 17:34:38.872    [main] DEBUG logger_name:c.b.m.e.s.MybatisSqlSessionFactoryBean - message:Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@376b6d7d'
2025-08-18 17:34:38.874    [main] DEBUG logger_name:com.zaxxer.hikari.HikariConfig - message:MyHikariCP - configuration:
2025-08-18 17:34:38.878    [main] DEBUG logger_name:com.zaxxer.hikari.HikariConfig - message:allowPoolSuspension................................false
2025-08-18 17:34:38.878    [main] DEBUG logger_name:com.zaxxer.hikari.HikariConfig - message:autoCommit................................true
2025-08-18 17:34:38.878    [main] DEBUG logger_name:com.zaxxer.hikari.HikariConfig - message:catalog................................none
2025-08-18 17:34:38.878    [main] DEBUG logger_name:com.zaxxer.hikari.HikariConfig - message:connectionInitSql................................none
2025-08-18 17:34:38.878    [main] DEBUG logger_name:com.zaxxer.hikari.HikariConfig - message:connectionTestQuery................................none
2025-08-18 17:34:38.878    [main] DEBUG logger_name:com.zaxxer.hikari.HikariConfig - message:connectionTimeout................................30000
2025-08-18 17:34:38.878    [main] DEBUG logger_name:com.zaxxer.hikari.HikariConfig - message:dataSource................................none
2025-08-18 17:34:38.878    [main] DEBUG logger_name:com.zaxxer.hikari.HikariConfig - message:dataSourceClassName................................none
2025-08-18 17:34:38.878    [main] DEBUG logger_name:com.zaxxer.hikari.HikariConfig - message:dataSourceJNDI................................none
2025-08-18 17:34:38.878    [main] DEBUG logger_name:com.zaxxer.hikari.HikariConfig - message:dataSourceProperties................................{password=<masked>}
2025-08-18 17:34:38.878    [main] DEBUG logger_name:com.zaxxer.hikari.HikariConfig - message:driverClassName................................"com.mysql.cj.jdbc.Driver"
2025-08-18 17:34:38.878    [main] DEBUG logger_name:com.zaxxer.hikari.HikariConfig - message:exceptionOverrideClassName................................none
2025-08-18 17:34:38.878    [main] DEBUG logger_name:com.zaxxer.hikari.HikariConfig - message:healthCheckProperties................................{}
2025-08-18 17:34:38.878    [main] DEBUG logger_name:com.zaxxer.hikari.HikariConfig - message:healthCheckRegistry................................none
2025-08-18 17:34:38.878    [main] DEBUG logger_name:com.zaxxer.hikari.HikariConfig - message:idleTimeout................................***********-08-18 17:34:38.878    [main] DEBUG logger_name:com.zaxxer.hikari.HikariConfig - message:initializationFailTimeout................................1
2025-08-18 17:34:38.879    [main] DEBUG logger_name:com.zaxxer.hikari.HikariConfig - message:isolateInternalQueries................................false
2025-08-18 17:34:38.879    [main] DEBUG logger_name:com.zaxxer.hikari.HikariConfig - message:jdbcUrl................................*************************************************************************************************************************************************************************************************************************
2025-08-18 17:34:38.879    [main] DEBUG logger_name:com.zaxxer.hikari.HikariConfig - message:keepaliveTime................................0
2025-08-18 17:34:38.879    [main] DEBUG logger_name:com.zaxxer.hikari.HikariConfig - message:leakDetectionThreshold................................0
2025-08-18 17:34:38.879    [main] DEBUG logger_name:com.zaxxer.hikari.HikariConfig - message:maxLifetime................................30000
2025-08-18 17:34:38.879    [main] DEBUG logger_name:com.zaxxer.hikari.HikariConfig - message:maximumPoolSize................................10
2025-08-18 17:34:38.879    [main] DEBUG logger_name:com.zaxxer.hikari.HikariConfig - message:metricRegistry................................none
2025-08-18 17:34:38.879    [main] DEBUG logger_name:com.zaxxer.hikari.HikariConfig - message:metricsTrackerFactory................................none
2025-08-18 17:34:38.879    [main] DEBUG logger_name:com.zaxxer.hikari.HikariConfig - message:minimumIdle................................10
2025-08-18 17:34:38.879    [main] DEBUG logger_name:com.zaxxer.hikari.HikariConfig - message:password................................<masked>
2025-08-18 17:34:38.879    [main] DEBUG logger_name:com.zaxxer.hikari.HikariConfig - message:poolName................................"MyHikariCP"
2025-08-18 17:34:38.879    [main] DEBUG logger_name:com.zaxxer.hikari.HikariConfig - message:readOnly................................false
2025-08-18 17:34:38.879    [main] DEBUG logger_name:com.zaxxer.hikari.HikariConfig - message:registerMbeans................................false
2025-08-18 17:34:38.879    [main] DEBUG logger_name:com.zaxxer.hikari.HikariConfig - message:scheduledExecutor................................none
2025-08-18 17:34:38.879    [main] DEBUG logger_name:com.zaxxer.hikari.HikariConfig - message:schema................................none
2025-08-18 17:34:38.879    [main] DEBUG logger_name:com.zaxxer.hikari.HikariConfig - message:threadFactory................................internal
2025-08-18 17:34:38.879    [main] DEBUG logger_name:com.zaxxer.hikari.HikariConfig - message:transactionIsolation................................default
2025-08-18 17:34:38.879    [main] DEBUG logger_name:com.zaxxer.hikari.HikariConfig - message:username................................"root"
2025-08-18 17:34:38.879    [main] DEBUG logger_name:com.zaxxer.hikari.HikariConfig - message:validationTimeout................................30000
2025-08-18 17:34:38.879    [main] INFO  logger_name:com.zaxxer.hikari.HikariDataSource - message:MyHikariCP - Starting...
2025-08-18 17:34:39.410    [main] INFO  logger_name:com.zaxxer.hikari.HikariDataSource - message:MyHikariCP - Start completed.
2025-08-18 17:34:39.784    [main] DEBUG logger_name:c.b.m.e.s.MybatisSqlSessionFactoryBean - message:Parsed mapper file: 'file [D:\my\jvs-car\car-biz\target\classes\mapper\biz\ModelMapper.xml]'
2025-08-18 17:34:39.971    [main] DEBUG logger_name:c.b.m.e.s.MybatisSqlSessionFactoryBean - message:Parsed mapper file: 'file [D:\my\jvs-car\car-biz\target\classes\mapper\exam\ExamDepartMapper.xml]'
2025-08-18 17:34:40.038    [main] DEBUG logger_name:c.b.m.e.s.MybatisSqlSessionFactoryBean - message:Parsed mapper file: 'file [D:\my\jvs-car\car-biz\target\classes\mapper\exam\ExamMapper.xml]'
2025-08-18 17:34:40.088    [main] DEBUG logger_name:c.b.m.e.s.MybatisSqlSessionFactoryBean - message:Parsed mapper file: 'file [D:\my\jvs-car\car-biz\target\classes\mapper\exam\ExamRepoMapper.xml]'
2025-08-18 17:34:40.144    [main] DEBUG logger_name:c.b.m.e.s.MybatisSqlSessionFactoryBean - message:Parsed mapper file: 'file [D:\my\jvs-car\car-biz\target\classes\mapper\paper\PaperMapper.xml]'
2025-08-18 17:34:40.192    [main] DEBUG logger_name:c.b.m.e.s.MybatisSqlSessionFactoryBean - message:Parsed mapper file: 'file [D:\my\jvs-car\car-biz\target\classes\mapper\paper\PaperQuAnswerMapper.xml]'
2025-08-18 17:34:40.241    [main] DEBUG logger_name:c.b.m.e.s.MybatisSqlSessionFactoryBean - message:Parsed mapper file: 'file [D:\my\jvs-car\car-biz\target\classes\mapper\paper\PaperQuMapper.xml]'
2025-08-18 17:34:40.288    [main] DEBUG logger_name:c.b.m.e.s.MybatisSqlSessionFactoryBean - message:Parsed mapper file: 'file [D:\my\jvs-car\car-biz\target\classes\mapper\qu\QuAnswerMapper.xml]'
2025-08-18 17:34:40.340    [main] DEBUG logger_name:c.b.m.e.s.MybatisSqlSessionFactoryBean - message:Parsed mapper file: 'file [D:\my\jvs-car\car-biz\target\classes\mapper\qu\QuMapper.xml]'
2025-08-18 17:34:40.380    [main] DEBUG logger_name:c.b.m.e.s.MybatisSqlSessionFactoryBean - message:Parsed mapper file: 'file [D:\my\jvs-car\car-biz\target\classes\mapper\qu\QuRepoMapper.xml]'
2025-08-18 17:34:40.436    [main] DEBUG logger_name:c.b.m.e.s.MybatisSqlSessionFactoryBean - message:Parsed mapper file: 'file [D:\my\jvs-car\car-biz\target\classes\mapper\repo\RepoMapper.xml]'
2025-08-18 17:34:40.516    [main] DEBUG logger_name:c.b.m.e.s.MybatisSqlSessionFactoryBean - message:Parsed mapper file: 'file [D:\my\jvs-car\car-biz\target\classes\mapper\sys\depart\SysDepartMapper.xml]'
2025-08-18 17:34:40.521    [main] DEBUG logger_name:c.b.m.e.s.MybatisSqlSessionFactoryBean - message:Parsed mapper file: 'file [D:\my\jvs-car\car-biz\target\classes\mapper\sys\system\SysDictMapper.xml]'
2025-08-18 17:34:40.566    [main] DEBUG logger_name:c.b.m.e.s.MybatisSqlSessionFactoryBean - message:Parsed mapper file: 'file [D:\my\jvs-car\car-biz\target\classes\mapper\sys\user\SysRoleMapper.xml]'
2025-08-18 17:34:40.615    [main] DEBUG logger_name:c.b.m.e.s.MybatisSqlSessionFactoryBean - message:Parsed mapper file: 'file [D:\my\jvs-car\car-biz\target\classes\mapper\sys\user\SysUserMapper.xml]'
2025-08-18 17:34:40.657    [main] DEBUG logger_name:c.b.m.e.s.MybatisSqlSessionFactoryBean - message:Parsed mapper file: 'file [D:\my\jvs-car\car-biz\target\classes\mapper\sys\user\SysUserRoleMapper.xml]'
2025-08-18 17:34:40.703    [main] DEBUG logger_name:c.b.m.e.s.MybatisSqlSessionFactoryBean - message:Parsed mapper file: 'file [D:\my\jvs-car\car-biz\target\classes\mapper\user\UserBookMapper.xml]'
2025-08-18 17:34:40.757    [main] DEBUG logger_name:c.b.m.e.s.MybatisSqlSessionFactoryBean - message:Parsed mapper file: 'file [D:\my\jvs-car\car-biz\target\classes\mapper\user\UserExamMapper.xml]'
2025-08-18 17:34:42.120    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'sqlSessionTemplate'
2025-08-18 17:34:42.120    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'sqlSessionTemplate' via factory method to bean named 'sqlSessionFactory'
2025-08-18 17:34:42.189    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 's3'
2025-08-18 17:34:42.189    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'cn.bctools.oss.config.OssAutoConfiguration'
2025-08-18 17:34:42.190    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'oss-cn.bctools.oss.props.OssProperties'
2025-08-18 17:34:42.199    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'cn.bctools.oss.config.OssAutoConfiguration' via constructor to bean named 'oss-cn.bctools.oss.props.OssProperties'
2025-08-18 17:34:42.205    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'fileDataInterface'
2025-08-18 17:34:42.208    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'cn.bctools.auth.api.api.FileApi'
2025-08-18 17:34:42.213    [main] DEBUG logger_name:o.s.c.a.AnnotationConfigApplicationContext - message:Refreshing FeignContext-file
2025-08-18 17:34:42.214    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.context.annotation.internalConfigurationAnnotationProcessor'
2025-08-18 17:34:42.230    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'propertySourcesPlaceholderConfigurer'
2025-08-18 17:34:42.230    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.context.event.internalEventListenerProcessor'
2025-08-18 17:34:42.230    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.context.event.internalEventListenerFactory'
2025-08-18 17:34:42.230    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.context.annotation.internalAutowiredAnnotationProcessor'
2025-08-18 17:34:42.232    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.context.annotation.internalCommonAnnotationProcessor'
2025-08-18 17:34:42.232    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'propertyPlaceholderAutoConfiguration'
2025-08-18 17:34:42.232    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'feignClientsConfiguration'
2025-08-18 17:34:42.233    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.openfeign.FeignClientsConfiguration$DefaultFeignBuilderConfiguration'
2025-08-18 17:34:42.233    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'feignQueryMapEncoderPageable'
2025-08-18 17:34:42.233    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'feignContract'
2025-08-18 17:34:42.234    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'feignConversionService'
2025-08-18 17:34:42.234    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'feignContract' via factory method to bean named 'feignConversionService'
2025-08-18 17:34:42.234    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'feignRetryer'
2025-08-18 17:34:42.234    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'feignLoggerFactory'
2025-08-18 17:34:42.234    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'feignClientConfigurer'
2025-08-18 17:34:42.234    [main] DEBUG logger_name:o.s.c.e.PropertySourcesPropertyResolver - message:Found key 'spring.liveBeansView.mbeanDomain' in PropertySource 'systemProperties' with value of type String
2025-08-18 17:34:42.249    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'fileDataInterface' via factory method to bean named 'cn.bctools.auth.api.api.FileApi'
2025-08-18 17:34:42.255    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'cn.bctools.redis.utils.RedisUtils'
2025-08-18 17:34:42.263    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'redisTemplate'
2025-08-18 17:34:42.263    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'cn.bctools.redis.config.RedisConfiguration'
2025-08-18 17:34:42.272    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'redisConnectionFactory'
2025-08-18 17:34:42.272    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration'
2025-08-18 17:34:42.273    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties'
2025-08-18 17:34:42.287    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' via constructor to bean named 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties'
2025-08-18 17:34:42.297    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'lettuceClientResources'
2025-08-18 17:34:42.321    [main] DEBUG logger_name:i.n.u.i.l.InternalLoggerFactory - message:Using SLF4J as the default logging framework
2025-08-18 17:34:42.385    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'braveTraceLettuceClientResourcesBuilderCustomizer'
2025-08-18 17:34:42.385    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.sleuth.autoconfig.instrument.redis.TraceRedisAutoConfiguration$LettuceConfiguration'
2025-08-18 17:34:42.389    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'lettuceBraveTracing'
2025-08-18 17:34:42.389    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.sleuth.autoconfig.brave.instrument.redis.BraveRedisAutoConfiguration$NewBraveLettuceConfiguration'
2025-08-18 17:34:42.393    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'spring.sleuth.redis-org.springframework.cloud.sleuth.autoconfig.brave.instrument.redis.TraceRedisProperties'
2025-08-18 17:34:42.396    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'lettuceBraveTracing' via factory method to bean named 'tracing'
2025-08-18 17:34:42.396    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'lettuceBraveTracing' via factory method to bean named 'spring.sleuth.redis-org.springframework.cloud.sleuth.autoconfig.brave.instrument.redis.TraceRedisProperties'
2025-08-18 17:34:42.422    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'braveTraceLettuceClientResourcesBuilderCustomizer' via factory method to bean named 'lettuceBraveTracing'
2025-08-18 17:34:42.422    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'braveTraceLettuceClientResourcesBuilderCustomizer' via factory method to bean named 'sleuthTraceSampler'
2025-08-18 17:34:42.507    [main] DEBUG logger_name:i.n.u.concurrent.GlobalEventExecutor - message:-Dio.netty.globalEventExecutor.quietPeriodSeconds: 1
2025-08-18 17:34:42.549    [main] DEBUG logger_name:i.n.u.i.InternalThreadLocalMap - message:-Dio.netty.threadLocalMap.stringBuilder.initialSize: 1024
2025-08-18 17:34:42.549    [main] DEBUG logger_name:i.n.u.i.InternalThreadLocalMap - message:-Dio.netty.threadLocalMap.stringBuilder.maxSize: 4096
2025-08-18 17:34:42.578    [main] DEBUG logger_name:io.netty.util.ResourceLeakDetector - message:-Dio.netty.leakDetection.level: simple
2025-08-18 17:34:42.578    [main] DEBUG logger_name:io.netty.util.ResourceLeakDetector - message:-Dio.netty.leakDetection.targetRecords: 4
2025-08-18 17:34:42.582    [main] DEBUG logger_name:i.n.util.ResourceLeakDetectorFactory - message:Loaded default ResourceLeakDetector: io.netty.util.ResourceLeakDetector@67c1f5be
2025-08-18 17:34:42.611    [main] DEBUG logger_name:i.n.util.internal.PlatformDependent0 - message:-Dio.netty.noUnsafe: false
2025-08-18 17:34:42.611    [main] DEBUG logger_name:i.n.util.internal.PlatformDependent0 - message:Java version: 8
2025-08-18 17:34:42.615    [main] DEBUG logger_name:i.n.util.internal.PlatformDependent0 - message:sun.misc.Unsafe.theUnsafe: available
2025-08-18 17:34:42.617    [main] DEBUG logger_name:i.n.util.internal.PlatformDependent0 - message:sun.misc.Unsafe.copyMemory: available
2025-08-18 17:34:42.618    [main] DEBUG logger_name:i.n.util.internal.PlatformDependent0 - message:sun.misc.Unsafe.storeFence: available
2025-08-18 17:34:42.620    [main] DEBUG logger_name:i.n.util.internal.PlatformDependent0 - message:java.nio.Buffer.address: available
2025-08-18 17:34:42.622    [main] DEBUG logger_name:i.n.util.internal.PlatformDependent0 - message:direct buffer constructor: available
2025-08-18 17:34:42.624    [main] DEBUG logger_name:i.n.util.internal.PlatformDependent0 - message:java.nio.Bits.unaligned: available, true
2025-08-18 17:34:42.624    [main] DEBUG logger_name:i.n.util.internal.PlatformDependent0 - message:jdk.internal.misc.Unsafe.allocateUninitializedArray(int): unavailable prior to Java9
2025-08-18 17:34:42.624    [main] DEBUG logger_name:i.n.util.internal.PlatformDependent0 - message:java.nio.DirectByteBuffer.<init>(long, int): available
2025-08-18 17:34:42.624    [main] DEBUG logger_name:i.n.util.internal.PlatformDependent - message:sun.misc.Unsafe: available
2025-08-18 17:34:42.625    [main] DEBUG logger_name:i.n.util.internal.PlatformDependent - message:-Dio.netty.tmpdir: C:\Users\<USER>\AppData\Local\Temp (java.io.tmpdir)
2025-08-18 17:34:42.625    [main] DEBUG logger_name:i.n.util.internal.PlatformDependent - message:-Dio.netty.bitMode: 64 (sun.arch.data.model)
2025-08-18 17:34:42.625    [main] DEBUG logger_name:i.n.util.internal.PlatformDependent - message:Platform: Windows
2025-08-18 17:34:42.630    [main] DEBUG logger_name:i.n.util.internal.PlatformDependent - message:-Dio.netty.maxDirectMemory: 7451181056 bytes
2025-08-18 17:34:42.630    [main] DEBUG logger_name:i.n.util.internal.PlatformDependent - message:-Dio.netty.uninitializedArrayAllocationThreshold: -1
2025-08-18 17:34:42.633    [main] DEBUG logger_name:io.netty.util.internal.CleanerJava6 - message:java.nio.ByteBuffer.cleaner(): available
2025-08-18 17:34:42.633    [main] DEBUG logger_name:i.n.util.internal.PlatformDependent - message:-Dio.netty.noPreferDirect: false
2025-08-18 17:34:42.640    [main] DEBUG logger_name:i.n.util.internal.PlatformDependent - message:org.jctools-core.MpscChunkedArrayQueue: available
2025-08-18 17:34:42.783    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'redisConnectionFactory' via factory method to bean named 'lettuceClientResources'
2025-08-18 17:34:43.163    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'redisTemplate' via factory method to bean named 'redisConnectionFactory'
2025-08-18 17:34:43.294    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class [B as writing converter
2025-08-18 17:34:43.294    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.lang.String as reading converter
2025-08-18 17:34:43.294    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.Number to class [B as writing converter
2025-08-18 17:34:43.294    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.lang.Number as reading converter
2025-08-18 17:34:43.294    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.Enum to class [B as writing converter
2025-08-18 17:34:43.294    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.lang.Enum as reading converter
2025-08-18 17:34:43.294    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.Boolean to class [B as writing converter
2025-08-18 17:34:43.294    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.lang.Boolean as reading converter
2025-08-18 17:34:43.294    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class [B as writing converter
2025-08-18 17:34:43.294    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.util.Date as reading converter
2025-08-18 17:34:43.294    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.UUID to class [B as writing converter
2025-08-18 17:34:43.294    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.util.UUID as reading converter
2025-08-18 17:34:43.294    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDateTime to class [B as writing converter
2025-08-18 17:34:43.294    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.LocalDateTime as reading converter
2025-08-18 17:34:43.294    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDate to class [B as writing converter
2025-08-18 17:34:43.294    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.LocalDate as reading converter
2025-08-18 17:34:43.294    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalTime to class [B as writing converter
2025-08-18 17:34:43.294    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.LocalTime as reading converter
2025-08-18 17:34:43.294    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.ZonedDateTime to class [B as writing converter
2025-08-18 17:34:43.294    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.ZonedDateTime as reading converter
2025-08-18 17:34:43.294    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Instant to class [B as writing converter
2025-08-18 17:34:43.294    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.Instant as reading converter
2025-08-18 17:34:43.294    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.ZoneId to class [B as writing converter
2025-08-18 17:34:43.294    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.ZoneId as reading converter
2025-08-18 17:34:43.294    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Period to class [B as writing converter
2025-08-18 17:34:43.294    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.Period as reading converter
2025-08-18 17:34:43.294    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Duration to class [B as writing converter
2025-08-18 17:34:43.294    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.Duration as reading converter
2025-08-18 17:34:43.294    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.joda.time.LocalDate to class java.util.Date as writing converter
2025-08-18 17:34:43.294    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.joda.time.LocalDateTime to class java.util.Date as writing converter
2025-08-18 17:34:43.294    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.joda.time.DateTime to class java.util.Date as writing converter
2025-08-18 17:34:43.294    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class org.joda.time.LocalDate as reading converter
2025-08-18 17:34:43.294    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class org.joda.time.LocalDateTime as reading converter
2025-08-18 17:34:43.294    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class org.joda.time.DateTime as reading converter
2025-08-18 17:34:43.294    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter
2025-08-18 17:34:43.294    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDateTime to class org.joda.time.DateTime as reading converter
2025-08-18 17:34:43.294    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Instant to class org.joda.time.LocalDateTime as reading converter
2025-08-18 17:34:43.294    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.joda.time.LocalDateTime to class java.time.Instant as writing converter
2025-08-18 17:34:43.294    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.joda.time.LocalDateTime to class java.time.LocalDateTime as writing converter
2025-08-18 17:34:43.294    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class java.time.LocalDateTime as reading converter
2025-08-18 17:34:43.294    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDateTime to class java.util.Date as writing converter
2025-08-18 17:34:43.294    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class java.time.LocalDate as reading converter
2025-08-18 17:34:43.294    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDate to class java.util.Date as writing converter
2025-08-18 17:34:43.294    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class java.time.LocalTime as reading converter
2025-08-18 17:34:43.294    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalTime to class java.util.Date as writing converter
2025-08-18 17:34:43.294    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class java.time.Instant as reading converter
2025-08-18 17:34:43.294    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Instant to class java.util.Date as writing converter
2025-08-18 17:34:43.294    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDateTime to class java.time.Instant as reading converter
2025-08-18 17:34:43.294    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Instant to class java.time.LocalDateTime as reading converter
2025-08-18 17:34:43.294    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.ZoneId to class java.lang.String as writing converter
2025-08-18 17:34:43.295    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.ZoneId as reading converter
2025-08-18 17:34:43.295    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Duration to class java.lang.String as writing converter
2025-08-18 17:34:43.295    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.Duration as reading converter
2025-08-18 17:34:43.295    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Period to class java.lang.String as writing converter
2025-08-18 17:34:43.295    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.Period as reading converter
2025-08-18 17:34:43.295    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.LocalDate as reading converter
2025-08-18 17:34:43.295    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.LocalDateTime as reading converter
2025-08-18 17:34:43.295    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.Instant as reading converter
2025-08-18 17:34:43.295    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class [B as writing converter
2025-08-18 17:34:43.295    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.lang.String as reading converter
2025-08-18 17:34:43.295    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.Number to class [B as writing converter
2025-08-18 17:34:43.295    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.lang.Number as reading converter
2025-08-18 17:34:43.295    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.Enum to class [B as writing converter
2025-08-18 17:34:43.295    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.lang.Enum as reading converter
2025-08-18 17:34:43.295    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.Boolean to class [B as writing converter
2025-08-18 17:34:43.295    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.lang.Boolean as reading converter
2025-08-18 17:34:43.295    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class [B as writing converter
2025-08-18 17:34:43.295    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.util.Date as reading converter
2025-08-18 17:34:43.295    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.UUID to class [B as writing converter
2025-08-18 17:34:43.295    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.util.UUID as reading converter
2025-08-18 17:34:43.295    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDateTime to class [B as writing converter
2025-08-18 17:34:43.295    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.LocalDateTime as reading converter
2025-08-18 17:34:43.295    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDate to class [B as writing converter
2025-08-18 17:34:43.295    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.LocalDate as reading converter
2025-08-18 17:34:43.296    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalTime to class [B as writing converter
2025-08-18 17:34:43.296    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.LocalTime as reading converter
2025-08-18 17:34:43.296    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.ZonedDateTime to class [B as writing converter
2025-08-18 17:34:43.296    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.ZonedDateTime as reading converter
2025-08-18 17:34:43.296    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Instant to class [B as writing converter
2025-08-18 17:34:43.296    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.Instant as reading converter
2025-08-18 17:34:43.296    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.ZoneId to class [B as writing converter
2025-08-18 17:34:43.296    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.ZoneId as reading converter
2025-08-18 17:34:43.296    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Period to class [B as writing converter
2025-08-18 17:34:43.296    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.Period as reading converter
2025-08-18 17:34:43.296    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Duration to class [B as writing converter
2025-08-18 17:34:43.296    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.Duration as reading converter
2025-08-18 17:34:43.296    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.joda.time.LocalDate to class java.util.Date as writing converter
2025-08-18 17:34:43.296    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.joda.time.LocalDateTime to class java.util.Date as writing converter
2025-08-18 17:34:43.296    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.joda.time.DateTime to class java.util.Date as writing converter
2025-08-18 17:34:43.296    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class org.joda.time.LocalDate as reading converter
2025-08-18 17:34:43.296    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class org.joda.time.LocalDateTime as reading converter
2025-08-18 17:34:43.296    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class org.joda.time.DateTime as reading converter
2025-08-18 17:34:43.296    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter
2025-08-18 17:34:43.296    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDateTime to class org.joda.time.DateTime as reading converter
2025-08-18 17:34:43.296    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Instant to class org.joda.time.LocalDateTime as reading converter
2025-08-18 17:34:43.296    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.joda.time.LocalDateTime to class java.time.Instant as writing converter
2025-08-18 17:34:43.296    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.joda.time.LocalDateTime to class java.time.LocalDateTime as writing converter
2025-08-18 17:34:43.296    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class java.time.LocalDateTime as reading converter
2025-08-18 17:34:43.296    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDateTime to class java.util.Date as writing converter
2025-08-18 17:34:43.296    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class java.time.LocalDate as reading converter
2025-08-18 17:34:43.296    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDate to class java.util.Date as writing converter
2025-08-18 17:34:43.296    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class java.time.LocalTime as reading converter
2025-08-18 17:34:43.296    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalTime to class java.util.Date as writing converter
2025-08-18 17:34:43.296    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class java.time.Instant as reading converter
2025-08-18 17:34:43.296    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Instant to class java.util.Date as writing converter
2025-08-18 17:34:43.296    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDateTime to class java.time.Instant as reading converter
2025-08-18 17:34:43.296    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Instant to class java.time.LocalDateTime as reading converter
2025-08-18 17:34:43.296    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.ZoneId to class java.lang.String as writing converter
2025-08-18 17:34:43.296    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.ZoneId as reading converter
2025-08-18 17:34:43.296    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Duration to class java.lang.String as writing converter
2025-08-18 17:34:43.296    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.Duration as reading converter
2025-08-18 17:34:43.296    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Period to class java.lang.String as writing converter
2025-08-18 17:34:43.296    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.Period as reading converter
2025-08-18 17:34:43.296    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.LocalDate as reading converter
2025-08-18 17:34:43.296    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.LocalDateTime as reading converter
2025-08-18 17:34:43.296    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.Instant as reading converter
2025-08-18 17:34:43.316    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class [B as writing converter
2025-08-18 17:34:43.316    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.lang.String as reading converter
2025-08-18 17:34:43.316    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.Number to class [B as writing converter
2025-08-18 17:34:43.316    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.lang.Number as reading converter
2025-08-18 17:34:43.316    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.Enum to class [B as writing converter
2025-08-18 17:34:43.316    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.lang.Enum as reading converter
2025-08-18 17:34:43.316    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.Boolean to class [B as writing converter
2025-08-18 17:34:43.316    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.lang.Boolean as reading converter
2025-08-18 17:34:43.316    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class [B as writing converter
2025-08-18 17:34:43.316    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.util.Date as reading converter
2025-08-18 17:34:43.316    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.UUID to class [B as writing converter
2025-08-18 17:34:43.316    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.util.UUID as reading converter
2025-08-18 17:34:43.316    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDateTime to class [B as writing converter
2025-08-18 17:34:43.316    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.LocalDateTime as reading converter
2025-08-18 17:34:43.316    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDate to class [B as writing converter
2025-08-18 17:34:43.316    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.LocalDate as reading converter
2025-08-18 17:34:43.316    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalTime to class [B as writing converter
2025-08-18 17:34:43.316    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.LocalTime as reading converter
2025-08-18 17:34:43.316    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.ZonedDateTime to class [B as writing converter
2025-08-18 17:34:43.316    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.ZonedDateTime as reading converter
2025-08-18 17:34:43.316    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Instant to class [B as writing converter
2025-08-18 17:34:43.316    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.Instant as reading converter
2025-08-18 17:34:43.316    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.ZoneId to class [B as writing converter
2025-08-18 17:34:43.316    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.ZoneId as reading converter
2025-08-18 17:34:43.316    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Period to class [B as writing converter
2025-08-18 17:34:43.316    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.Period as reading converter
2025-08-18 17:34:43.316    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Duration to class [B as writing converter
2025-08-18 17:34:43.316    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.Duration as reading converter
2025-08-18 17:34:43.316    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.joda.time.LocalDate to class java.util.Date as writing converter
2025-08-18 17:34:43.316    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.joda.time.LocalDateTime to class java.util.Date as writing converter
2025-08-18 17:34:43.316    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.joda.time.DateTime to class java.util.Date as writing converter
2025-08-18 17:34:43.316    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class org.joda.time.LocalDate as reading converter
2025-08-18 17:34:43.316    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class org.joda.time.LocalDateTime as reading converter
2025-08-18 17:34:43.316    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class org.joda.time.DateTime as reading converter
2025-08-18 17:34:43.316    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter
2025-08-18 17:34:43.316    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDateTime to class org.joda.time.DateTime as reading converter
2025-08-18 17:34:43.316    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Instant to class org.joda.time.LocalDateTime as reading converter
2025-08-18 17:34:43.316    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.joda.time.LocalDateTime to class java.time.Instant as writing converter
2025-08-18 17:34:43.317    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.joda.time.LocalDateTime to class java.time.LocalDateTime as writing converter
2025-08-18 17:34:43.317    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class java.time.LocalDateTime as reading converter
2025-08-18 17:34:43.317    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDateTime to class java.util.Date as writing converter
2025-08-18 17:34:43.317    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class java.time.LocalDate as reading converter
2025-08-18 17:34:43.317    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDate to class java.util.Date as writing converter
2025-08-18 17:34:43.317    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class java.time.LocalTime as reading converter
2025-08-18 17:34:43.317    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalTime to class java.util.Date as writing converter
2025-08-18 17:34:43.317    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class java.time.Instant as reading converter
2025-08-18 17:34:43.317    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Instant to class java.util.Date as writing converter
2025-08-18 17:34:43.317    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDateTime to class java.time.Instant as reading converter
2025-08-18 17:34:43.317    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Instant to class java.time.LocalDateTime as reading converter
2025-08-18 17:34:43.317    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.ZoneId to class java.lang.String as writing converter
2025-08-18 17:34:43.317    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.ZoneId as reading converter
2025-08-18 17:34:43.317    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Duration to class java.lang.String as writing converter
2025-08-18 17:34:43.317    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.Duration as reading converter
2025-08-18 17:34:43.317    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Period to class java.lang.String as writing converter
2025-08-18 17:34:43.317    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.Period as reading converter
2025-08-18 17:34:43.317    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.LocalDate as reading converter
2025-08-18 17:34:43.317    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.LocalDateTime as reading converter
2025-08-18 17:34:43.317    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.Instant as reading converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class [B as writing converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.lang.String as reading converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.Number to class [B as writing converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.lang.Number as reading converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.Enum to class [B as writing converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.lang.Enum as reading converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.Boolean to class [B as writing converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.lang.Boolean as reading converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class [B as writing converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.util.Date as reading converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.UUID to class [B as writing converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.util.UUID as reading converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDateTime to class [B as writing converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.LocalDateTime as reading converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDate to class [B as writing converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.LocalDate as reading converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalTime to class [B as writing converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.LocalTime as reading converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.ZonedDateTime to class [B as writing converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.ZonedDateTime as reading converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Instant to class [B as writing converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.Instant as reading converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.ZoneId to class [B as writing converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.ZoneId as reading converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Period to class [B as writing converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.Period as reading converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Duration to class [B as writing converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.Duration as reading converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.joda.time.LocalDate to class java.util.Date as writing converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.joda.time.LocalDateTime to class java.util.Date as writing converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.joda.time.DateTime to class java.util.Date as writing converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class org.joda.time.LocalDate as reading converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class org.joda.time.LocalDateTime as reading converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class org.joda.time.DateTime as reading converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDateTime to class org.joda.time.DateTime as reading converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Instant to class org.joda.time.LocalDateTime as reading converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.joda.time.LocalDateTime to class java.time.Instant as writing converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.joda.time.LocalDateTime to class java.time.LocalDateTime as writing converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class java.time.LocalDateTime as reading converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDateTime to class java.util.Date as writing converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class java.time.LocalDate as reading converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDate to class java.util.Date as writing converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class java.time.LocalTime as reading converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalTime to class java.util.Date as writing converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class java.time.Instant as reading converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Instant to class java.util.Date as writing converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDateTime to class java.time.Instant as reading converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Instant to class java.time.LocalDateTime as reading converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.ZoneId to class java.lang.String as writing converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.ZoneId as reading converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Duration to class java.lang.String as writing converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.Duration as reading converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Period to class java.lang.String as writing converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.Period as reading converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.LocalDate as reading converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.LocalDateTime as reading converter
2025-08-18 17:34:43.322    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.Instant as reading converter
2025-08-18 17:34:43.329    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class [B as writing converter
2025-08-18 17:34:43.329    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.lang.String as reading converter
2025-08-18 17:34:43.329    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.Number to class [B as writing converter
2025-08-18 17:34:43.329    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.lang.Number as reading converter
2025-08-18 17:34:43.329    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.Enum to class [B as writing converter
2025-08-18 17:34:43.329    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.lang.Enum as reading converter
2025-08-18 17:34:43.329    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.Boolean to class [B as writing converter
2025-08-18 17:34:43.329    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.lang.Boolean as reading converter
2025-08-18 17:34:43.329    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class [B as writing converter
2025-08-18 17:34:43.329    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.util.Date as reading converter
2025-08-18 17:34:43.329    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.UUID to class [B as writing converter
2025-08-18 17:34:43.329    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.util.UUID as reading converter
2025-08-18 17:34:43.329    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDateTime to class [B as writing converter
2025-08-18 17:34:43.329    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.LocalDateTime as reading converter
2025-08-18 17:34:43.329    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDate to class [B as writing converter
2025-08-18 17:34:43.329    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.LocalDate as reading converter
2025-08-18 17:34:43.329    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalTime to class [B as writing converter
2025-08-18 17:34:43.329    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.LocalTime as reading converter
2025-08-18 17:34:43.329    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.ZonedDateTime to class [B as writing converter
2025-08-18 17:34:43.329    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.ZonedDateTime as reading converter
2025-08-18 17:34:43.329    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Instant to class [B as writing converter
2025-08-18 17:34:43.329    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.Instant as reading converter
2025-08-18 17:34:43.329    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.ZoneId to class [B as writing converter
2025-08-18 17:34:43.329    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.ZoneId as reading converter
2025-08-18 17:34:43.329    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Period to class [B as writing converter
2025-08-18 17:34:43.329    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.Period as reading converter
2025-08-18 17:34:43.329    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Duration to class [B as writing converter
2025-08-18 17:34:43.329    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.Duration as reading converter
2025-08-18 17:34:43.329    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.joda.time.LocalDate to class java.util.Date as writing converter
2025-08-18 17:34:43.329    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.joda.time.LocalDateTime to class java.util.Date as writing converter
2025-08-18 17:34:43.329    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.joda.time.DateTime to class java.util.Date as writing converter
2025-08-18 17:34:43.329    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class org.joda.time.LocalDate as reading converter
2025-08-18 17:34:43.329    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class org.joda.time.LocalDateTime as reading converter
2025-08-18 17:34:43.329    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class org.joda.time.DateTime as reading converter
2025-08-18 17:34:43.329    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter
2025-08-18 17:34:43.330    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDateTime to class org.joda.time.DateTime as reading converter
2025-08-18 17:34:43.330    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Instant to class org.joda.time.LocalDateTime as reading converter
2025-08-18 17:34:43.330    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.joda.time.LocalDateTime to class java.time.Instant as writing converter
2025-08-18 17:34:43.330    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.joda.time.LocalDateTime to class java.time.LocalDateTime as writing converter
2025-08-18 17:34:43.330    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class java.time.LocalDateTime as reading converter
2025-08-18 17:34:43.330    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDateTime to class java.util.Date as writing converter
2025-08-18 17:34:43.330    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class java.time.LocalDate as reading converter
2025-08-18 17:34:43.330    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDate to class java.util.Date as writing converter
2025-08-18 17:34:43.330    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class java.time.LocalTime as reading converter
2025-08-18 17:34:43.330    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalTime to class java.util.Date as writing converter
2025-08-18 17:34:43.330    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class java.time.Instant as reading converter
2025-08-18 17:34:43.330    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Instant to class java.util.Date as writing converter
2025-08-18 17:34:43.330    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDateTime to class java.time.Instant as reading converter
2025-08-18 17:34:43.330    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Instant to class java.time.LocalDateTime as reading converter
2025-08-18 17:34:43.330    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.ZoneId to class java.lang.String as writing converter
2025-08-18 17:34:43.330    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.ZoneId as reading converter
2025-08-18 17:34:43.330    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Duration to class java.lang.String as writing converter
2025-08-18 17:34:43.330    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.Duration as reading converter
2025-08-18 17:34:43.330    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Period to class java.lang.String as writing converter
2025-08-18 17:34:43.330    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.Period as reading converter
2025-08-18 17:34:43.330    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.LocalDate as reading converter
2025-08-18 17:34:43.330    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.LocalDateTime as reading converter
2025-08-18 17:34:43.330    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.Instant as reading converter
2025-08-18 17:34:43.488    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'redisLockUtil'
2025-08-18 17:34:43.490    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'redisLockUtil' via constructor to bean named 'redisTemplate'
2025-08-18 17:34:43.499    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 's3' via factory method to bean named 'fileDataInterface'
2025-08-18 17:34:43.499    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 's3' via factory method to bean named 'cn.bctools.redis.utils.RedisUtils'
2025-08-18 17:34:43.868    [main] DEBUG logger_name:com.amazonaws.AmazonWebServiceClient - message:Internal logging successfully configured to commons logger: true
2025-08-18 17:34:43.906    [main] DEBUG logger_name:com.amazonaws.metrics.AwsSdkMetrics - message:Admin mbean registered under com.amazonaws.management:type=AwsSdkMetrics
2025-08-18 17:34:44.383    [java-sdk-http-connection-reaper] DEBUG logger_name:o.a.h.i.c.PoolingHttpClientConnectionManager - message:Closing connections idle longer than 60000 MILLISECONDS
2025-08-18 17:34:44.387    [main] DEBUG logger_name:c.a.m.CsmConfigurationProviderChain - message:Unable to load configuration from com.amazonaws.monitoring.EnvironmentVariableCsmConfigurationProvider@20983174: Unable to load Client Side Monitoring configurations from environment variables!
2025-08-18 17:34:44.387    [main] DEBUG logger_name:c.a.m.CsmConfigurationProviderChain - message:Unable to load configuration from com.amazonaws.monitoring.SystemPropertyCsmConfigurationProvider@35ab67d4: Unable to load Client Side Monitoring configurations from system properties variables!
2025-08-18 17:34:44.390    [main] DEBUG logger_name:c.a.m.CsmConfigurationProviderChain - message:Unable to load configuration from com.amazonaws.monitoring.ProfileCsmConfigurationProvider@2857dbe3: Unable to load config file
2025-08-18 17:34:44.768    [main] DEBUG logger_name:com.amazonaws.request - message:Sending Request: GET https://gyczcmn.cqtransit.com / Headers: (amz-sdk-invocation-id: 40464e29-a2a6-3180-fc3d-30d5494c4dbe, Content-Type: application/octet-stream, User-Agent: aws-sdk-java/1.11.939 Windows_10/10.0 Java_HotSpot(TM)_64-Bit_Server_VM/25.152-b16 java/1.8.0_152 kotlin/1.6.21 vendor/Oracle_Corporation, ) 
2025-08-18 17:34:44.932    [main] DEBUG logger_name:com.amazonaws.auth.AWS4Signer - message:AWS4 Canonical Request: '"GET
/

amz-sdk-invocation-id:40464e29-a2a6-3180-fc3d-30d5494c4dbe
amz-sdk-request:attempt=1;max=4
amz-sdk-retry:0/0/500
content-type:application/octet-stream
host:gyczcmn.cqtransit.com
user-agent:aws-sdk-java/1.11.939 Windows_10/10.0 Java_HotSpot(TM)_64-Bit_Server_VM/25.152-b16 java/1.8.0_152 kotlin/1.6.21 vendor/Oracle_Corporation
x-amz-content-sha256:UNSIGNED-PAYLOAD
x-amz-date:20250818T093444Z

amz-sdk-invocation-id;amz-sdk-request;amz-sdk-retry;content-type;host;user-agent;x-amz-content-sha256;x-amz-date
UNSIGNED-PAYLOAD"
2025-08-18 17:34:44.932    [main] DEBUG logger_name:com.amazonaws.auth.AWS4Signer - message:AWS4 String to Sign: '"AWS4-HMAC-SHA256
20250818T093444Z
20250818/us-east-1/s3/aws4_request
2d60cf1374cc5ccd4e3be3e7c4c8de96fc3387bb5502406d8a458cee81ef9b2b"
2025-08-18 17:34:44.932    [main] DEBUG logger_name:com.amazonaws.auth.AWS4Signer - message:Generating a new signing key as the signing key not available in the cache for the date 1755475200000
2025-08-18 17:34:45.092    [main] DEBUG logger_name:o.a.h.c.protocol.RequestAddCookies - message:CookieSpec selected: default
2025-08-18 17:34:45.154    [main] DEBUG logger_name:o.a.h.c.protocol.RequestAuthCache - message:Auth cache not set in the context
2025-08-18 17:34:45.168    [main] DEBUG logger_name:o.a.h.i.c.PoolingHttpClientConnectionManager - message:Connection request: [route: {s}->https://gyczcmn.cqtransit.com:443][total available: 0; route allocated: 0 of 50; total allocated: 0 of 50]
2025-08-18 17:34:45.251    [main] DEBUG logger_name:o.a.h.i.c.PoolingHttpClientConnectionManager - message:Connection leased: [id: 0][route: {s}->https://gyczcmn.cqtransit.com:443][total available: 0; route allocated: 1 of 50; total allocated: 1 of 50]
2025-08-18 17:34:45.264    [main] DEBUG logger_name:o.a.h.impl.execchain.MainClientExec - message:Opening connection {s}->https://gyczcmn.cqtransit.com:443
2025-08-18 17:34:45.271    [main] DEBUG logger_name:o.a.h.i.c.DefaultHttpClientConnectionOperator - message:Connecting to gyczcmn.cqtransit.com/*************:443
2025-08-18 17:34:45.271    [main] DEBUG logger_name:c.a.h.conn.ssl.SdkTLSSocketFactory - message:connecting to gyczcmn.cqtransit.com/*************:443
2025-08-18 17:34:45.271    [main] DEBUG logger_name:c.a.h.conn.ssl.SdkTLSSocketFactory - message:Connecting socket to gyczcmn.cqtransit.com/*************:443 with timeout 10000
2025-08-18 17:34:45.309    [main] DEBUG logger_name:c.a.h.conn.ssl.SdkTLSSocketFactory - message:Enabled protocols: [TLSv1, TLSv1.1, TLSv1.2]
2025-08-18 17:34:45.309    [main] DEBUG logger_name:c.a.h.conn.ssl.SdkTLSSocketFactory - message:Enabled cipher suites:[TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA256, TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256, TLS_RSA_WITH_AES_128_CBC_SHA256, TLS_ECDH_ECDSA_WITH_AES_128_CBC_SHA256, TLS_ECDH_RSA_WITH_AES_128_CBC_SHA256, TLS_DHE_RSA_WITH_AES_128_CBC_SHA256, TLS_DHE_DSS_WITH_AES_128_CBC_SHA256, TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA, TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA, TLS_RSA_WITH_AES_128_CBC_SHA, TLS_ECDH_ECDSA_WITH_AES_128_CBC_SHA, TLS_ECDH_RSA_WITH_AES_128_CBC_SHA, TLS_DHE_RSA_WITH_AES_128_CBC_SHA, TLS_DHE_DSS_WITH_AES_128_CBC_SHA, TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256, TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256, TLS_RSA_WITH_AES_128_GCM_SHA256, TLS_ECDH_ECDSA_WITH_AES_128_GCM_SHA256, TLS_ECDH_RSA_WITH_AES_128_GCM_SHA256, TLS_DHE_RSA_WITH_AES_128_GCM_SHA256, TLS_DHE_DSS_WITH_AES_128_GCM_SHA256, TLS_EMPTY_RENEGOTIATION_INFO_SCSV]
2025-08-18 17:34:45.309    [main] DEBUG logger_name:c.a.h.conn.ssl.SdkTLSSocketFactory - message:socket.getSupportedProtocols(): [SSLv2Hello, SSLv3, TLSv1, TLSv1.1, TLSv1.2], socket.getEnabledProtocols(): [TLSv1, TLSv1.1, TLSv1.2]
2025-08-18 17:34:45.312    [main] DEBUG logger_name:c.a.h.conn.ssl.SdkTLSSocketFactory - message:TLS protocol enabled for SSL handshake: [TLSv1.2, TLSv1.1, TLSv1]
2025-08-18 17:34:45.312    [main] DEBUG logger_name:c.a.h.conn.ssl.SdkTLSSocketFactory - message:Starting handshake
2025-08-18 17:34:45.403    [main] DEBUG logger_name:c.a.h.conn.ssl.SdkTLSSocketFactory - message:Secure session established
2025-08-18 17:34:45.403    [main] DEBUG logger_name:c.a.h.conn.ssl.SdkTLSSocketFactory - message: negotiated protocol: TLSv1.2
2025-08-18 17:34:45.403    [main] DEBUG logger_name:c.a.h.conn.ssl.SdkTLSSocketFactory - message: negotiated cipher suite: TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256
2025-08-18 17:34:45.403    [main] DEBUG logger_name:c.a.h.conn.ssl.SdkTLSSocketFactory - message: peer principal: CN=*.cqtransit.com, O=重庆物流集团有限公司, ST=重庆市, C=CN
2025-08-18 17:34:45.403    [main] DEBUG logger_name:c.a.h.conn.ssl.SdkTLSSocketFactory - message: peer alternative names: [*.cqtransit.com, cqtransit.com]
2025-08-18 17:34:45.403    [main] DEBUG logger_name:c.a.h.conn.ssl.SdkTLSSocketFactory - message: issuer principal: CN=GeoTrust G2 TLS CN RSA4096 SHA256 2022 CA1, O="DigiCert, Inc.", C=US
2025-08-18 17:34:45.431    [main] DEBUG logger_name:com.amazonaws.internal.SdkSSLSocket - message:created: gyczcmn.cqtransit.com/*************:443
2025-08-18 17:34:45.433    [main] DEBUG logger_name:o.a.h.i.c.DefaultHttpClientConnectionOperator - message:Connection established ***********:52695<->*************:443
2025-08-18 17:34:45.433    [main] DEBUG logger_name:o.a.h.i.c.DefaultManagedHttpClientConnection - message:http-outgoing-0: set socket timeout to 50000
2025-08-18 17:34:45.434    [main] DEBUG logger_name:o.a.h.impl.execchain.MainClientExec - message:Executing request GET / HTTP/1.1
2025-08-18 17:34:45.434    [main] DEBUG logger_name:o.a.h.impl.execchain.MainClientExec - message:Proxy auth state: UNCHALLENGED
2025-08-18 17:34:45.443    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 >> GET / HTTP/1.1
2025-08-18 17:34:45.443    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 >> Host: gyczcmn.cqtransit.com
2025-08-18 17:34:45.443    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 >> amz-sdk-invocation-id: 40464e29-a2a6-3180-fc3d-30d5494c4dbe
2025-08-18 17:34:45.443    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 >> amz-sdk-request: attempt=1;max=4
2025-08-18 17:34:45.443    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 >> amz-sdk-retry: 0/0/500
2025-08-18 17:34:45.443    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 >> Authorization: AWS4-HMAC-SHA256 Credential=miniominio/20250818/us-east-1/s3/aws4_request, SignedHeaders=amz-sdk-invocation-id;amz-sdk-request;amz-sdk-retry;content-type;host;user-agent;x-amz-content-sha256;x-amz-date, Signature=b6e500d600b6c2087480a466c814b7903cc1c96e0f0953d0b01109793a4aae7d
2025-08-18 17:34:45.443    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 >> Content-Type: application/octet-stream
2025-08-18 17:34:45.444    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 >> User-Agent: aws-sdk-java/1.11.939 Windows_10/10.0 Java_HotSpot(TM)_64-Bit_Server_VM/25.152-b16 java/1.8.0_152 kotlin/1.6.21 vendor/Oracle_Corporation
2025-08-18 17:34:45.444    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 >> x-amz-content-sha256: UNSIGNED-PAYLOAD
2025-08-18 17:34:45.444    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 >> X-Amz-Date: 20250818T093444Z
2025-08-18 17:34:45.444    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 >> Content-Length: 0
2025-08-18 17:34:45.444    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 >> Connection: Keep-Alive
2025-08-18 17:34:45.505    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 << HTTP/1.1 200 OK
2025-08-18 17:34:45.505    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 << Date: Mon, 18 Aug 2025 09:34:46 GMT
2025-08-18 17:34:45.505    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 << Content-Type: application/xml
2025-08-18 17:34:45.505    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 << Content-Length: 662
2025-08-18 17:34:45.505    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 << Connection: keep-alive
2025-08-18 17:34:45.505    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 << Set-Cookie: acw_tc=0bce952217555096861688922ed413d4dc09574383412ff4f60460f35ac25a;path=/;HttpOnly;Max-Age=1800
2025-08-18 17:34:45.505    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 << Accept-Ranges: bytes
2025-08-18 17:34:45.505    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 << Content-Security-Policy: block-all-mixed-content
2025-08-18 17:34:45.505    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 << Strict-Transport-Security: max-age=31536000; includeSubDomains
2025-08-18 17:34:45.505    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 << Vary: Origin
2025-08-18 17:34:45.505    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 << Vary: Accept-Encoding
2025-08-18 17:34:45.505    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 << X-Amz-Request-Id: 185CD2C22DB37479
2025-08-18 17:34:45.505    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 << X-Content-Type-Options: nosniff
2025-08-18 17:34:45.505    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 << X-Xss-Protection: 1; mode=block
2025-08-18 17:34:45.505    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 << Set-Cookie: SERVERID=695009b291c41f819daf32bfa60f4214|1755509686|1755509686;Path=/
2025-08-18 17:34:45.538    [main] DEBUG logger_name:o.a.h.impl.execchain.MainClientExec - message:Connection can be kept alive for 60000 MILLISECONDS
2025-08-18 17:34:45.566    [cluster-ClusterId{value='68a2f3ab92e95743146149d3', description='null'}-localhost:27017] DEBUG logger_name:org.mongodb.driver.cluster - message:Updating cluster description to  {type=STANDALONE, servers=[{address=localhost:27017, type=STANDALONE, roundTripTime=113.4 ms, state=CONNECTED}]
2025-08-18 17:34:45.566    [cluster-ClusterId{value='68a2f3ab92e95743146149d3', description='null'}-localhost:27017] DEBUG logger_name:org.mongodb.driver.cluster - message:Checking status of localhost:27017
2025-08-18 17:34:45.583    [cluster-rtt-ClusterId{value='68a2f3ab92e95743146149d3', description='null'}-localhost:27017] DEBUG logger_name:org.mongodb.driver.protocol.command - message:Sending command '{"hello": 1, "$db": "admin", "$readPreference": {"mode": "primaryPreferred"}}' with request id 4 to database admin on connection [connectionId{localValue:1, serverValue:22}] to server localhost:27017
2025-08-18 17:34:45.584    [cluster-rtt-ClusterId{value='68a2f3ab92e95743146149d3', description='null'}-localhost:27017] DEBUG logger_name:org.mongodb.driver.protocol.command - message:Execution of command with request id 4 completed successfully in 51.83 ms on connection [connectionId{localValue:1, serverValue:22}] to server localhost:27017
2025-08-18 17:34:45.597    [main] DEBUG logger_name:o.a.h.c.p.ResponseProcessCookies - message:Cookie accepted [acw_tc="0bce952217555096861688922ed413d4dc09574383412ff4f60460f35ac25a", version:0, domain:gyczcmn.cqtransit.com, path:/, expiry:null]
2025-08-18 17:34:45.597    [main] DEBUG logger_name:o.a.h.c.p.ResponseProcessCookies - message:Cookie accepted [SERVERID="695009b291c41f819daf32bfa60f4214|1755509686|1755509686", version:0, domain:gyczcmn.cqtransit.com, path:/, expiry:null]
2025-08-18 17:34:45.602    [main] DEBUG logger_name:c.amazonaws.retry.ClockSkewAdjuster - message:Reported server date (from 'Date' header): Mon, 18 Aug 2025 09:34:46 GMT
2025-08-18 17:34:45.697    [main] DEBUG logger_name:c.a.s.s.m.t.XmlResponsesSaxParser - message:Sanitizing XML document destined for handler class com.amazonaws.services.s3.model.transform.XmlResponsesSaxParser$ListAllMyBucketsHandler
2025-08-18 17:34:45.700    [main] DEBUG logger_name:o.a.h.i.c.PoolingHttpClientConnectionManager - message:Connection [id: 0][route: {s}->https://gyczcmn.cqtransit.com:443] can be kept alive for 60.0 seconds
2025-08-18 17:34:45.700    [main] DEBUG logger_name:o.a.h.i.c.DefaultManagedHttpClientConnection - message:http-outgoing-0: set socket timeout to 0
2025-08-18 17:34:45.700    [main] DEBUG logger_name:o.a.h.i.c.PoolingHttpClientConnectionManager - message:Connection released: [id: 0][route: {s}->https://gyczcmn.cqtransit.com:443][total available: 1; route allocated: 1 of 50; total allocated: 1 of 50]
2025-08-18 17:34:45.700    [main] DEBUG logger_name:c.a.s.s.m.t.XmlResponsesSaxParser - message:Parsing XML response document with handler: class com.amazonaws.services.s3.model.transform.XmlResponsesSaxParser$ListAllMyBucketsHandler
2025-08-18 17:34:45.706    [main] DEBUG logger_name:com.amazonaws.request - message:Received successful response: 200, AWS Request ID: 185CD2C22DB37479
2025-08-18 17:34:45.706    [main] DEBUG logger_name:com.amazonaws.requestId - message:x-amzn-RequestId: not available
2025-08-18 17:34:45.706    [main] DEBUG logger_name:com.amazonaws.requestId - message:AWS Request ID: 185CD2C22DB37479
2025-08-18 17:34:46.070    [main] DEBUG logger_name:com.amazonaws.request - message:Sending Request: PUT https://gyczcmn.cqtransit.com /jvs-public/ Parameters: ({"policy":[null]}Headers: (amz-sdk-invocation-id: 6906569d-d3ba-bf22-cb94-0e3b6ba1bd63, Content-MD5: yRHTWLgNF++TX6Q0/QAZLQ==, Content-Type: application/octet-stream, User-Agent: aws-sdk-java/1.11.939 Windows_10/10.0 Java_HotSpot(TM)_64-Bit_Server_VM/25.152-b16 java/1.8.0_152 kotlin/1.6.21 vendor/Oracle_Corporation, ) 
2025-08-18 17:34:46.071    [main] DEBUG logger_name:com.amazonaws.auth.AWS4Signer - message:AWS4 Canonical Request: '"PUT
/jvs-public/
policy=
amz-sdk-invocation-id:6906569d-d3ba-bf22-cb94-0e3b6ba1bd63
amz-sdk-request:ttl=20250818T093536Z;attempt=1;max=4
amz-sdk-retry:0/0/500
content-md5:yRHTWLgNF++TX6Q0/QAZLQ==
content-type:application/octet-stream
host:gyczcmn.cqtransit.com
user-agent:aws-sdk-java/1.11.939 Windows_10/10.0 Java_HotSpot(TM)_64-Bit_Server_VM/25.152-b16 java/1.8.0_152 kotlin/1.6.21 vendor/Oracle_Corporation
x-amz-content-sha256:UNSIGNED-PAYLOAD
x-amz-date:20250818T093446Z

amz-sdk-invocation-id;amz-sdk-request;amz-sdk-retry;content-md5;content-type;host;user-agent;x-amz-content-sha256;x-amz-date
UNSIGNED-PAYLOAD"
2025-08-18 17:34:46.071    [main] DEBUG logger_name:com.amazonaws.auth.AWS4Signer - message:AWS4 String to Sign: '"AWS4-HMAC-SHA256
20250818T093446Z
20250818/us-east-1/s3/aws4_request
b7a7d13934b9bb46f25aadf2dbc4bab36d1e6a8fa9c96959a5230f3cb690168d"
2025-08-18 17:34:46.094    [main] DEBUG logger_name:o.a.h.c.protocol.RequestAddCookies - message:CookieSpec selected: default
2025-08-18 17:34:46.096    [main] DEBUG logger_name:o.a.h.c.protocol.RequestAddCookies - message:Cookie [version: 0][name: SERVERID][value: 695009b291c41f819daf32bfa60f4214|1755509686|1755509686][domain: gyczcmn.cqtransit.com][path: /][expiry: null] match [(secure)gyczcmn.cqtransit.com:443/jvs-public/]
2025-08-18 17:34:46.096    [main] DEBUG logger_name:o.a.h.c.protocol.RequestAddCookies - message:Cookie [version: 0][name: acw_tc][value: 0bce952217555096861688922ed413d4dc09574383412ff4f60460f35ac25a][domain: gyczcmn.cqtransit.com][path: /][expiry: null] match [(secure)gyczcmn.cqtransit.com:443/jvs-public/]
2025-08-18 17:34:46.096    [main] DEBUG logger_name:o.a.h.c.protocol.RequestAuthCache - message:Auth cache not set in the context
2025-08-18 17:34:46.096    [main] DEBUG logger_name:o.a.h.i.c.PoolingHttpClientConnectionManager - message:Connection request: [route: {s}->https://gyczcmn.cqtransit.com:443][total available: 1; route allocated: 1 of 50; total allocated: 1 of 50]
2025-08-18 17:34:46.097    [main] DEBUG logger_name:o.a.h.i.c.PoolingHttpClientConnectionManager - message:Connection leased: [id: 0][route: {s}->https://gyczcmn.cqtransit.com:443][total available: 0; route allocated: 1 of 50; total allocated: 1 of 50]
2025-08-18 17:34:46.097    [main] DEBUG logger_name:o.a.h.i.c.DefaultManagedHttpClientConnection - message:http-outgoing-0: set socket timeout to 50000
2025-08-18 17:34:46.097    [main] DEBUG logger_name:o.a.h.i.c.DefaultManagedHttpClientConnection - message:http-outgoing-0: set socket timeout to 50000
2025-08-18 17:34:46.097    [main] DEBUG logger_name:o.a.h.impl.execchain.MainClientExec - message:Executing request PUT /jvs-public/?policy HTTP/1.1
2025-08-18 17:34:46.097    [main] DEBUG logger_name:o.a.h.impl.execchain.MainClientExec - message:Proxy auth state: UNCHALLENGED
2025-08-18 17:34:46.097    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 >> PUT /jvs-public/?policy HTTP/1.1
2025-08-18 17:34:46.097    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 >> Host: gyczcmn.cqtransit.com
2025-08-18 17:34:46.097    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 >> amz-sdk-invocation-id: 6906569d-d3ba-bf22-cb94-0e3b6ba1bd63
2025-08-18 17:34:46.097    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 >> amz-sdk-request: ttl=20250818T093536Z;attempt=1;max=4
2025-08-18 17:34:46.097    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 >> amz-sdk-retry: 0/0/500
2025-08-18 17:34:46.097    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 >> Authorization: AWS4-HMAC-SHA256 Credential=miniominio/20250818/us-east-1/s3/aws4_request, SignedHeaders=amz-sdk-invocation-id;amz-sdk-request;amz-sdk-retry;content-md5;content-type;host;user-agent;x-amz-content-sha256;x-amz-date, Signature=d375c33ac5962ed82b03b235bd19048fe05b0d397a5f7dae6935d95905283d1f
2025-08-18 17:34:46.097    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 >> Content-MD5: yRHTWLgNF++TX6Q0/QAZLQ==
2025-08-18 17:34:46.097    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 >> Content-Type: application/octet-stream
2025-08-18 17:34:46.097    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 >> User-Agent: aws-sdk-java/1.11.939 Windows_10/10.0 Java_HotSpot(TM)_64-Bit_Server_VM/25.152-b16 java/1.8.0_152 kotlin/1.6.21 vendor/Oracle_Corporation
2025-08-18 17:34:46.097    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 >> x-amz-content-sha256: UNSIGNED-PAYLOAD
2025-08-18 17:34:46.097    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 >> X-Amz-Date: 20250818T093446Z
2025-08-18 17:34:46.097    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 >> Content-Length: 919
2025-08-18 17:34:46.097    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 >> Connection: Keep-Alive
2025-08-18 17:34:46.097    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 >> Expect: 100-continue
2025-08-18 17:34:46.097    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 >> Cookie: SERVERID=695009b291c41f819daf32bfa60f4214|1755509686|1755509686; acw_tc=0bce952217555096861688922ed413d4dc09574383412ff4f60460f35ac25a
2025-08-18 17:34:46.110    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 << HTTP/1.1 100 Continue
2025-08-18 17:34:46.142    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 << HTTP/1.1 204 No Content
2025-08-18 17:34:46.142    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 << Date: Mon, 18 Aug 2025 09:34:46 GMT
2025-08-18 17:34:46.142    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 << Connection: keep-alive
2025-08-18 17:34:46.142    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 << Accept-Ranges: bytes
2025-08-18 17:34:46.142    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 << Content-Security-Policy: block-all-mixed-content
2025-08-18 17:34:46.142    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 << Strict-Transport-Security: max-age=31536000; includeSubDomains
2025-08-18 17:34:46.142    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 << Vary: Origin
2025-08-18 17:34:46.142    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 << Vary: Accept-Encoding
2025-08-18 17:34:46.142    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 << X-Amz-Request-Id: 185CD2C254604ADF
2025-08-18 17:34:46.142    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 << X-Content-Type-Options: nosniff
2025-08-18 17:34:46.142    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 << X-Xss-Protection: 1; mode=block
2025-08-18 17:34:46.142    [main] DEBUG logger_name:org.apache.http.headers - message:http-outgoing-0 << Set-Cookie: SERVERID=695009b291c41f819daf32bfa60f4214|1755509686|1755509686;Path=/
2025-08-18 17:34:46.142    [main] DEBUG logger_name:o.a.h.impl.execchain.MainClientExec - message:Connection can be kept alive for 60000 MILLISECONDS
2025-08-18 17:34:46.142    [main] DEBUG logger_name:o.a.h.i.c.PoolingHttpClientConnectionManager - message:Connection [id: 0][route: {s}->https://gyczcmn.cqtransit.com:443] can be kept alive for 60.0 seconds
2025-08-18 17:34:46.142    [main] DEBUG logger_name:o.a.h.i.c.DefaultManagedHttpClientConnection - message:http-outgoing-0: set socket timeout to 0
2025-08-18 17:34:46.142    [main] DEBUG logger_name:o.a.h.i.c.PoolingHttpClientConnectionManager - message:Connection released: [id: 0][route: {s}->https://gyczcmn.cqtransit.com:443][total available: 1; route allocated: 1 of 50; total allocated: 1 of 50]
2025-08-18 17:34:46.142    [main] DEBUG logger_name:o.a.h.c.p.ResponseProcessCookies - message:Cookie accepted [SERVERID="695009b291c41f819daf32bfa60f4214|1755509686|1755509686", version:0, domain:gyczcmn.cqtransit.com, path:/, expiry:null]
2025-08-18 17:34:46.142    [main] DEBUG logger_name:c.amazonaws.retry.ClockSkewAdjuster - message:Reported server date (from 'Date' header): Mon, 18 Aug 2025 09:34:46 GMT
2025-08-18 17:34:46.142    [main] DEBUG logger_name:com.amazonaws.request - message:Received successful response: 204, AWS Request ID: 185CD2C254604ADF
2025-08-18 17:34:46.142    [main] DEBUG logger_name:com.amazonaws.requestId - message:x-amzn-RequestId: not available
2025-08-18 17:34:46.142    [main] DEBUG logger_name:com.amazonaws.requestId - message:AWS Request ID: 185CD2C254604ADF
2025-08-18 17:34:46.154    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'fileUploadController'
2025-08-18 17:34:46.163    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'fileUploadController' via constructor to bean named 'fileDataInterface'
2025-08-18 17:34:46.163    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'fileUploadController' via constructor to bean named 's3'
2025-08-18 17:34:46.163    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'fileUploadController' via constructor to bean named 'cn.bctools.redis.utils.RedisUtils'
2025-08-18 17:34:46.171    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'agreementController' via constructor to bean named 'agreementServiceImpl'
2025-08-18 17:34:46.171    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'agreementController' via constructor to bean named 's3'
2025-08-18 17:34:46.171    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'agreementController' via constructor to bean named 'cn.bctools.auth.api.api.AuthDeptServiceApi'
2025-08-18 17:34:46.171    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'agreementController' via constructor to bean named 'fileUploadController'
2025-08-18 17:34:46.202    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'baiduApiController'
2025-08-18 17:34:46.208    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'baiduApiController' via constructor to bean named 'fileUploadController'
2025-08-18 17:34:46.214    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'driverController'
2025-08-18 17:34:46.218    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'sysUserServiceImpl'
2025-08-18 17:34:46.235    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'sysUserMapper'
2025-08-18 17:34:46.261    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'sysUserRoleServiceImpl'
2025-08-18 17:34:46.264    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'sysUserRoleMapper'
2025-08-18 17:34:46.442    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'driverController' via constructor to bean named 'agreementServiceImpl'
2025-08-18 17:34:46.442    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'driverController' via constructor to bean named 'mongoTemplate'
2025-08-18 17:34:46.442    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'driverController' via constructor to bean named 'sysUserServiceImpl'
2025-08-18 17:34:46.442    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'driverController' via constructor to bean named 'modelMapper'
2025-08-18 17:34:46.454    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'transportController'
2025-08-18 17:34:46.457    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'transportController' via constructor to bean named 'mongoTemplate'
2025-08-18 17:34:46.468    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'serviceExceptionHandler'
2025-08-18 17:34:46.482    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'springUtils'
2025-08-18 17:34:46.486    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'dataBoardController'
2025-08-18 17:34:46.493    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'dataBoardServiceImpl'
2025-08-18 17:34:46.501    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'carLineMapper'
2025-08-18 17:34:46.546    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'carTypeNumMapper'
2025-08-18 17:34:46.592    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'subscribeMapper'
2025-08-18 17:34:46.633    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'subscribeAllMapper'
2025-08-18 17:34:46.677    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'driverInfoDayNumMapper'
2025-08-18 17:34:46.734    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'scheduleJobTask'
2025-08-18 17:34:46.739    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'examController'
2025-08-18 17:34:46.749    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'examServiceImpl'
2025-08-18 17:34:46.753    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'examMapper'
2025-08-18 17:34:46.770    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'examRepoServiceImpl'
2025-08-18 17:34:46.774    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'examRepoMapper'
2025-08-18 17:34:46.839    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'examDepartServiceImpl'
2025-08-18 17:34:46.844    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'examDepartMapper'
2025-08-18 17:34:46.979    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'driverDriController'
2025-08-18 17:34:46.983    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'driverServiceImpl'
2025-08-18 17:34:46.985    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'driverMapper'
2025-08-18 17:34:46.997    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'paperController'
2025-08-18 17:34:47.003    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'paperServiceImpl'
2025-08-18 17:34:47.011    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'paperMapper'
2025-08-18 17:34:47.025    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'quServiceImpl'
2025-08-18 17:34:47.031    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'quMapper'
2025-08-18 17:34:47.045    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'quAnswerServiceImpl'
2025-08-18 17:34:47.049    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'quAnswerMapper'
2025-08-18 17:34:47.115    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'quRepoServiceImpl'
2025-08-18 17:34:47.121    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'quRepoMapper'
2025-08-18 17:34:47.133    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'repoServiceImpl'
2025-08-18 17:34:47.136    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'repoMapper'
2025-08-18 17:34:47.382    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'paperQuServiceImpl'
2025-08-18 17:34:47.386    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'paperQuMapper'
2025-08-18 17:34:47.457    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'paperQuAnswerServiceImpl'
2025-08-18 17:34:47.461    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'paperQuAnswerMapper'
2025-08-18 17:34:47.525    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'userBookServiceImpl'
2025-08-18 17:34:47.529    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'userBookMapper'
2025-08-18 17:34:47.593    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'userExamServiceImpl'
2025-08-18 17:34:47.597    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'userExamMapper'
2025-08-18 17:34:47.680    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'breakExamJob'
2025-08-18 17:34:47.685    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'quController'
2025-08-18 17:34:47.693    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'repoController'
2025-08-18 17:34:47.697    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'sysConfigController'
2025-08-18 17:34:47.701    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'sysConfigServiceImpl'
2025-08-18 17:34:47.705    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'sysConfigMapper'
2025-08-18 17:34:47.800    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'sysDepartController'
2025-08-18 17:34:47.805    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'sysDepartServiceImpl'
2025-08-18 17:34:47.812    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'sysDepartMapper'
2025-08-18 17:34:47.891    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'sysDictServiceImpl'
2025-08-18 17:34:47.894    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'sysDictMapper'
2025-08-18 17:34:47.902    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'captchaController'
2025-08-18 17:34:47.907    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'captchaServiceImpl'
2025-08-18 17:34:47.914    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'epsAuthProperties'
2025-08-18 17:34:47.925    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'redisService'
2025-08-18 17:34:47.936    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'sysRoleController'
2025-08-18 17:34:47.939    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'sysRoleServiceImpl'
2025-08-18 17:34:47.942    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'sysRoleMapper'
2025-08-18 17:34:48.010    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'sysUserController'
2025-08-18 17:34:48.015    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'mongoUserServiceImpl'
2025-08-18 17:34:48.043    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'userBookController'
2025-08-18 17:34:48.049    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'userExamController'
2025-08-18 17:34:48.053    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'sensitiveDataConverter'
2025-08-18 17:34:48.066    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'listConvert'
2025-08-18 17:34:48.076    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'localDateTimeConvert'
2025-08-18 17:34:48.082    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'arrayListConvert'
2025-08-18 17:34:48.086    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'grayLoadBalancerClientConfiguration'
2025-08-18 17:34:48.094    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'serviceModelToSwagger2MapperImpl'
2025-08-18 17:34:48.110    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'compatibilityModelMapperImpl'
2025-08-18 17:34:48.117    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'securityMapperImpl'
2025-08-18 17:34:48.133    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'licenseMapperImpl'
2025-08-18 17:34:48.138    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'vendorExtensionsMapperImpl'
2025-08-18 17:34:48.149    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'propertyMapperImpl'
2025-08-18 17:34:48.158    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'parameterMapperImpl'
2025-08-18 17:34:48.164    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'modelMapperImpl'
2025-08-18 17:34:48.171    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'requestParameterMapperImpl'
2025-08-18 17:34:48.189    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'modelSpecificationMapperImpl'
2025-08-18 17:34:48.201    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'swagger2ControllerWebMvc'
2025-08-18 17:34:48.204    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'resourceGroupCache'
2025-08-18 17:34:48.204    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'springfox.documentation.spring.web.SpringfoxWebConfiguration'
2025-08-18 17:34:48.219    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'jsonSerializer'
2025-08-18 17:34:48.220    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'swagger2Module'
2025-08-18 17:34:48.220    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'springfox.documentation.swagger2.configuration.Swagger2DocumentationConfiguration'
2025-08-18 17:34:48.238    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'openApiModule'
2025-08-18 17:34:48.238    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'springfox.documentation.oas.configuration.OpenApiMappingConfiguration'
2025-08-18 17:34:48.255    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'jsonSerializer' via factory method to bean named 'swagger2Module'
2025-08-18 17:34:48.255    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'jsonSerializer' via factory method to bean named 'openApiModule'
2025-08-18 17:34:48.287    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'webMvcSwaggerTransformationFilterRegistry'
2025-08-18 17:34:48.350    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'swagger2ControllerWebMvc' via constructor to bean named 'resourceGroupCache'
2025-08-18 17:34:48.350    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'swagger2ControllerWebMvc' via constructor to bean named 'serviceModelToSwagger2MapperImpl'
2025-08-18 17:34:48.350    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'swagger2ControllerWebMvc' via constructor to bean named 'jsonSerializer'
2025-08-18 17:34:48.350    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'swagger2ControllerWebMvc' via constructor to bean named 'webMvcSwaggerTransformationFilterRegistry'
2025-08-18 17:34:48.353    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'openApiSchemaPropertyBuilder'
2025-08-18 17:34:48.360    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'descriptionResolver'
2025-08-18 17:34:48.360    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'descriptionResolver' via factory method to bean named 'environment'
2025-08-18 17:34:48.364    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'modelSpecificationFactory'
2025-08-18 17:34:48.370    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'typeNameExtractor'
2025-08-18 17:34:48.374    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'typeResolver'
2025-08-18 17:34:48.374    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'springfox.documentation.schema.configuration.ModelsConfiguration'
2025-08-18 17:34:48.390    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'typeNameProviderPluginRegistry'
2025-08-18 17:34:48.396    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'jacksonEnumTypeDeterminer'
2025-08-18 17:34:48.398    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'typeNameExtractor' via constructor to bean named 'typeResolver'
2025-08-18 17:34:48.398    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'typeNameExtractor' via constructor to bean named 'typeNameProviderPluginRegistry'
2025-08-18 17:34:48.398    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'typeNameExtractor' via constructor to bean named 'jacksonEnumTypeDeterminer'
2025-08-18 17:34:48.401    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'modelSpecificationFactory' via constructor to bean named 'typeNameExtractor'
2025-08-18 17:34:48.401    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'modelSpecificationFactory' via constructor to bean named 'jacksonEnumTypeDeterminer'
2025-08-18 17:34:48.403    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'openApiSchemaPropertyBuilder' via constructor to bean named 'descriptionResolver'
2025-08-18 17:34:48.403    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'openApiSchemaPropertyBuilder' via constructor to bean named 'modelSpecificationFactory'
2025-08-18 17:34:48.407    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'apiModelPropertyPropertyBuilder'
2025-08-18 17:34:48.409    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'apiModelPropertyPropertyBuilder' via constructor to bean named 'descriptionResolver'
2025-08-18 17:34:48.409    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'apiModelPropertyPropertyBuilder' via constructor to bean named 'modelSpecificationFactory'
2025-08-18 17:34:48.411    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'apiModelTypeNameProvider'
2025-08-18 17:34:48.415    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'apiModelBuilder'
2025-08-18 17:34:48.418    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'apiModelBuilder' via constructor to bean named 'typeResolver'
2025-08-18 17:34:48.418    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'apiModelBuilder' via constructor to bean named 'typeNameExtractor'
2025-08-18 17:34:48.418    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'apiModelBuilder' via constructor to bean named 'jacksonEnumTypeDeterminer'
2025-08-18 17:34:48.418    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'apiModelBuilder' via constructor to bean named 'modelSpecificationFactory'
2025-08-18 17:34:48.421    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'operationImplicitParameterReader'
2025-08-18 17:34:48.432    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'operationImplicitParameterReader' via constructor to bean named 'descriptionResolver'
2025-08-18 17:34:48.436    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'vendorExtensionsReader'
2025-08-18 17:34:48.442    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'swaggerOperationResponseClassReader'
2025-08-18 17:34:48.444    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'swaggerOperationResponseClassReader' via constructor to bean named 'typeResolver'
2025-08-18 17:34:48.444    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'swaggerOperationResponseClassReader' via constructor to bean named 'jacksonEnumTypeDeterminer'
2025-08-18 17:34:48.444    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'swaggerOperationResponseClassReader' via constructor to bean named 'typeNameExtractor'
2025-08-18 17:34:48.446    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'swaggerOperationModelsProvider'
2025-08-18 17:34:48.450    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'swaggerOperationModelsProvider' via constructor to bean named 'typeResolver'
2025-08-18 17:34:48.453    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'openApiOperationAuthReader'
2025-08-18 17:34:48.459    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'swaggerMediaTypeReader'
2025-08-18 17:34:48.462    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'operationHttpMethodReader'
2025-08-18 17:34:48.466    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'operationImplicitParametersReader'
2025-08-18 17:34:48.468    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'operationImplicitParametersReader' via constructor to bean named 'descriptionResolver'
2025-08-18 17:34:48.470    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'operationAuthReader'
2025-08-18 17:34:48.476    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'operationHiddenReader'
2025-08-18 17:34:48.479    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'operationSummaryReader'
2025-08-18 17:34:48.481    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'operationSummaryReader' via constructor to bean named 'descriptionResolver'
2025-08-18 17:34:48.483    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'openApiOperationTagsReader'
2025-08-18 17:34:48.489    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'swaggerResponseMessageReader'
2025-08-18 17:34:48.497    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'documentationPluginsManager'
2025-08-18 17:34:48.504    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'documentationPluginRegistry'
2025-08-18 17:34:48.511    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'apiListingBuilderPluginRegistry'
2025-08-18 17:34:48.517    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'parameterBuilderPluginRegistry'
2025-08-18 17:34:48.522    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'expandedParameterBuilderPluginRegistry'
2025-08-18 17:34:48.529    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'operationBuilderPluginRegistry'
2025-08-18 17:34:48.534    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'operationModelsProviderPluginRegistry'
2025-08-18 17:34:48.540    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'defaultsProviderPluginRegistry'
2025-08-18 17:34:48.546    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'pathDecoratorRegistry'
2025-08-18 17:34:48.551    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'apiListingScannerPluginRegistry'
2025-08-18 17:34:48.556    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'responseBuilderPluginRegistry'
2025-08-18 17:34:48.562    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'modelNamesRegistryFactoryPluginRegistry'
2025-08-18 17:34:48.569    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'swaggerResponseMessageReader' via constructor to bean named 'jacksonEnumTypeDeterminer'
2025-08-18 17:34:48.569    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'swaggerResponseMessageReader' via constructor to bean named 'typeNameExtractor'
2025-08-18 17:34:48.569    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'swaggerResponseMessageReader' via constructor to bean named 'typeResolver'
2025-08-18 17:34:48.569    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'swaggerResponseMessageReader' via constructor to bean named 'modelSpecificationFactory'
2025-08-18 17:34:48.569    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'swaggerResponseMessageReader' via constructor to bean named 'documentationPluginsManager'
2025-08-18 17:34:48.573    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'operationNicknameIntoUniqueIdReader'
2025-08-18 17:34:48.577    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'operationPositionReader'
2025-08-18 17:34:48.580    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'openApiResponseReader'
2025-08-18 17:34:48.583    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'openApiResponseReader' via constructor to bean named 'typeResolver'
2025-08-18 17:34:48.583    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'openApiResponseReader' via constructor to bean named 'modelSpecificationFactory'
2025-08-18 17:34:48.583    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'openApiResponseReader' via constructor to bean named 'documentationPluginsManager'
2025-08-18 17:34:48.586    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'operationNotesReader'
2025-08-18 17:34:48.587    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'operationNotesReader' via constructor to bean named 'descriptionResolver'
2025-08-18 17:34:48.589    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'swaggerOperationTagsReader'
2025-08-18 17:34:48.595    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'swaggerParameterDescriptionReader'
2025-08-18 17:34:48.598    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'swaggerParameterDescriptionReader' via constructor to bean named 'descriptionResolver'
2025-08-18 17:34:48.598    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'swaggerParameterDescriptionReader' via constructor to bean named 'jacksonEnumTypeDeterminer'
2025-08-18 17:34:48.600    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'swaggerExpandedParameterBuilder'
2025-08-18 17:34:48.603    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'swaggerExpandedParameterBuilder' via constructor to bean named 'descriptionResolver'
2025-08-18 17:34:48.603    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'swaggerExpandedParameterBuilder' via constructor to bean named 'jacksonEnumTypeDeterminer'
2025-08-18 17:34:48.606    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'openApiParameterBuilder'
2025-08-18 17:34:48.611    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'openApiParameterBuilder' via constructor to bean named 'descriptionResolver'
2025-08-18 17:34:48.614    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'swaggerApiListingReader'
2025-08-18 17:34:48.618    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'inMemorySwaggerResourcesProvider'
2025-08-18 17:34:48.621    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'inMemorySwaggerResourcesProvider' via constructor to bean named 'environment'
2025-08-18 17:34:48.621    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'inMemorySwaggerResourcesProvider' via constructor to bean named 'resourceGroupCache'
2025-08-18 17:34:48.621    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'inMemorySwaggerResourcesProvider' via constructor to bean named 'documentationPluginsManager'
2025-08-18 17:34:48.622    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'createRestApi'
2025-08-18 17:34:48.623    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'cn.bctools.feign.config.SwaggerAutoConfiguration'
2025-08-18 17:34:48.628    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'createRestApi' via factory method to bean named 'cn.bctools.feign.config.SwaggerProperties'
2025-08-18 17:34:48.628    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'createRestApi' via factory method to bean named 'nacosRegistration'
2025-08-18 17:34:48.705    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'apiResourceController'
2025-08-18 17:34:48.710    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'apiResourceController' via constructor to bean named 'inMemorySwaggerResourcesProvider'
2025-08-18 17:34:48.739    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'apiListingReferenceScanner'
2025-08-18 17:34:48.744    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'apiDocumentationScanner'
2025-08-18 17:34:48.748    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'apiListingScanner'
2025-08-18 17:34:48.751    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'apiDescriptionReader'
2025-08-18 17:34:48.755    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'cachingOperationReader'
2025-08-18 17:34:48.758    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'apiOperationReader'
2025-08-18 17:34:48.761    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'cachingOperationNameGenerator'
2025-08-18 17:34:48.764    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'apiOperationReader' via constructor to bean named 'documentationPluginsManager'
2025-08-18 17:34:48.764    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'apiOperationReader' via constructor to bean named 'cachingOperationNameGenerator'
2025-08-18 17:34:48.768    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'cachingOperationReader' via constructor to bean named 'apiOperationReader'
2025-08-18 17:34:48.771    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'apiDescriptionLookup'
2025-08-18 17:34:48.775    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'apiDescriptionReader' via constructor to bean named 'cachingOperationReader'
2025-08-18 17:34:48.775    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'apiDescriptionReader' via constructor to bean named 'documentationPluginsManager'
2025-08-18 17:34:48.775    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'apiDescriptionReader' via constructor to bean named 'apiDescriptionLookup'
2025-08-18 17:34:48.777    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'apiModelReader'
2025-08-18 17:34:48.786    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'cachingModelProvider'
2025-08-18 17:34:48.788    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'defaultModelProvider'
2025-08-18 17:34:48.791    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'cachingModelPropertiesProvider'
2025-08-18 17:34:48.793    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'optimized'
2025-08-18 17:34:48.802    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'accessorsProvider'
2025-08-18 17:34:48.804    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'accessorsProvider' via constructor to bean named 'typeResolver'
2025-08-18 17:34:48.807    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'fieldProvider'
2025-08-18 17:34:48.808    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'fieldProvider' via constructor to bean named 'typeResolver'
2025-08-18 17:34:48.811    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'factoryMethodProvider'
2025-08-18 17:34:48.813    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'factoryMethodProvider' via constructor to bean named 'typeResolver'
2025-08-18 17:34:48.819    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'objectMapperBeanPropertyNamingStrategy'
2025-08-18 17:34:48.829    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'schemaPluginsManager'
2025-08-18 17:34:48.832    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'modelPropertyBuilderPluginRegistry'
2025-08-18 17:34:48.837    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'modelBuilderPluginRegistry'
2025-08-18 17:34:48.843    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'viewProviderPluginRegistry'
2025-08-18 17:34:48.849    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'syntheticModelProviderPluginRegistry'
2025-08-18 17:34:48.854    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'schemaPluginsManager' via constructor to bean named 'modelPropertyBuilderPluginRegistry'
2025-08-18 17:34:48.854    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'schemaPluginsManager' via constructor to bean named 'modelBuilderPluginRegistry'
2025-08-18 17:34:48.854    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'schemaPluginsManager' via constructor to bean named 'viewProviderPluginRegistry'
2025-08-18 17:34:48.854    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'schemaPluginsManager' via constructor to bean named 'syntheticModelProviderPluginRegistry'
2025-08-18 17:34:48.857    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'optimized' via constructor to bean named 'accessorsProvider'
2025-08-18 17:34:48.857    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'optimized' via constructor to bean named 'fieldProvider'
2025-08-18 17:34:48.857    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'optimized' via constructor to bean named 'factoryMethodProvider'
2025-08-18 17:34:48.857    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'optimized' via constructor to bean named 'typeResolver'
2025-08-18 17:34:48.857    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'optimized' via constructor to bean named 'objectMapperBeanPropertyNamingStrategy'
2025-08-18 17:34:48.857    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'optimized' via constructor to bean named 'schemaPluginsManager'
2025-08-18 17:34:48.857    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'optimized' via constructor to bean named 'jacksonEnumTypeDeterminer'
2025-08-18 17:34:48.857    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'optimized' via constructor to bean named 'typeNameExtractor'
2025-08-18 17:34:48.857    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'optimized' via constructor to bean named 'modelSpecificationFactory'
2025-08-18 17:34:48.867    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'cachingModelPropertiesProvider' via constructor to bean named 'typeResolver'
2025-08-18 17:34:48.868    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'cachingModelPropertiesProvider' via constructor to bean named 'optimized'
2025-08-18 17:34:48.872    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'cachingModelDependencyProvider'
2025-08-18 17:34:48.876    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'defaultModelDependencyProvider'
2025-08-18 17:34:48.880    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'defaultModelDependencyProvider' via constructor to bean named 'typeResolver'
2025-08-18 17:34:48.880    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'defaultModelDependencyProvider' via constructor to bean named 'cachingModelPropertiesProvider'
2025-08-18 17:34:48.880    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'defaultModelDependencyProvider' via constructor to bean named 'typeNameExtractor'
2025-08-18 17:34:48.880    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'defaultModelDependencyProvider' via constructor to bean named 'jacksonEnumTypeDeterminer'
2025-08-18 17:34:48.880    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'defaultModelDependencyProvider' via constructor to bean named 'schemaPluginsManager'
2025-08-18 17:34:48.883    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'cachingModelDependencyProvider' via constructor to bean named 'defaultModelDependencyProvider'
2025-08-18 17:34:48.886    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'defaultModelProvider' via constructor to bean named 'cachingModelPropertiesProvider'
2025-08-18 17:34:48.886    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'defaultModelProvider' via constructor to bean named 'cachingModelDependencyProvider'
2025-08-18 17:34:48.886    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'defaultModelProvider' via constructor to bean named 'schemaPluginsManager'
2025-08-18 17:34:48.886    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'defaultModelProvider' via constructor to bean named 'typeNameExtractor'
2025-08-18 17:34:48.886    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'defaultModelProvider' via constructor to bean named 'jacksonEnumTypeDeterminer'
2025-08-18 17:34:48.890    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'defaultModelSpecificationProvider'
2025-08-18 17:34:48.896    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'defaultModelSpecificationProvider' via constructor to bean named 'typeResolver'
2025-08-18 17:34:48.896    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'defaultModelSpecificationProvider' via constructor to bean named 'cachingModelPropertiesProvider'
2025-08-18 17:34:48.896    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'defaultModelSpecificationProvider' via constructor to bean named 'cachingModelDependencyProvider'
2025-08-18 17:34:48.896    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'defaultModelSpecificationProvider' via constructor to bean named 'schemaPluginsManager'
2025-08-18 17:34:48.896    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'defaultModelSpecificationProvider' via constructor to bean named 'typeNameExtractor'
2025-08-18 17:34:48.896    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'defaultModelSpecificationProvider' via constructor to bean named 'jacksonEnumTypeDeterminer'
2025-08-18 17:34:48.896    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'defaultModelSpecificationProvider' via constructor to bean named 'modelSpecificationFactory'
2025-08-18 17:34:48.899    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'cachingModelProvider' via constructor to bean named 'defaultModelProvider'
2025-08-18 17:34:48.899    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'cachingModelProvider' via constructor to bean named 'defaultModelSpecificationProvider'
2025-08-18 17:34:48.902    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'apiModelReader' via constructor to bean named 'cachingModelProvider'
2025-08-18 17:34:48.903    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'apiModelReader' via constructor to bean named 'typeResolver'
2025-08-18 17:34:48.903    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'apiModelReader' via constructor to bean named 'documentationPluginsManager'
2025-08-18 17:34:48.903    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'apiModelReader' via constructor to bean named 'jacksonEnumTypeDeterminer'
2025-08-18 17:34:48.903    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'apiModelReader' via constructor to bean named 'typeNameExtractor'
2025-08-18 17:34:48.911    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'apiModelSpecificationReader'
2025-08-18 17:34:48.912    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'apiModelSpecificationReader' via constructor to bean named 'cachingModelProvider'
2025-08-18 17:34:48.912    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'apiModelSpecificationReader' via constructor to bean named 'documentationPluginsManager'
2025-08-18 17:34:48.912    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'apiModelSpecificationReader' via constructor to bean named 'typeResolver'
2025-08-18 17:34:48.914    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'apiListingScanner' via constructor to bean named 'apiDescriptionReader'
2025-08-18 17:34:48.914    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'apiListingScanner' via constructor to bean named 'apiModelReader'
2025-08-18 17:34:48.914    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'apiListingScanner' via constructor to bean named 'apiModelSpecificationReader'
2025-08-18 17:34:48.914    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'apiListingScanner' via constructor to bean named 'documentationPluginsManager'
2025-08-18 17:34:48.916    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'apiDocumentationScanner' via constructor to bean named 'apiListingReferenceScanner'
2025-08-18 17:34:48.916    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'apiDocumentationScanner' via constructor to bean named 'apiListingScanner'
2025-08-18 17:34:48.918    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'apiListingReader'
2025-08-18 17:34:48.922    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'mediaTypeReader'
2025-08-18 17:34:48.928    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'operationModelsProvider'
2025-08-18 17:34:48.931    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'operationModelsProvider' via constructor to bean named 'schemaPluginsManager'
2025-08-18 17:34:48.933    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'operationDeprecatedReader'
2025-08-18 17:34:48.936    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'responseMessagesReader'
2025-08-18 17:34:48.939    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'responseMessagesReader' via constructor to bean named 'jacksonEnumTypeDeterminer'
2025-08-18 17:34:48.939    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'responseMessagesReader' via constructor to bean named 'typeNameExtractor'
2025-08-18 17:34:48.939    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'responseMessagesReader' via constructor to bean named 'schemaPluginsManager'
2025-08-18 17:34:48.939    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'responseMessagesReader' via constructor to bean named 'modelSpecificationFactory'
2025-08-18 17:34:48.939    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'responseMessagesReader' via constructor to bean named 'documentationPluginsManager'
2025-08-18 17:34:48.942    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'operationParameterReader'
2025-08-18 17:34:48.945    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'modelAttributeParameterExpander'
2025-08-18 17:34:48.952    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'modelAttributeParameterExpander' via constructor to bean named 'fieldProvider'
2025-08-18 17:34:48.952    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'modelAttributeParameterExpander' via constructor to bean named 'accessorsProvider'
2025-08-18 17:34:48.952    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'modelAttributeParameterExpander' via constructor to bean named 'jacksonEnumTypeDeterminer'
2025-08-18 17:34:48.959    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'contentParameterAggregator'
2025-08-18 17:34:48.969    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'operationParameterReader' via constructor to bean named 'modelAttributeParameterExpander'
2025-08-18 17:34:48.969    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'operationParameterReader' via constructor to bean named 'jacksonEnumTypeDeterminer'
2025-08-18 17:34:48.969    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'operationParameterReader' via constructor to bean named 'contentParameterAggregator'
2025-08-18 17:34:48.972    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'operationTagsReader'
2025-08-18 17:34:48.976    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'defaultOperationReader'
2025-08-18 17:34:48.979    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'operationParameterRequestConditionReader'
2025-08-18 17:34:48.982    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'operationParameterRequestConditionReader' via constructor to bean named 'typeResolver'
2025-08-18 17:34:48.985    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'operationParameterHeadersConditionReader'
2025-08-18 17:34:48.987    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'operationParameterHeadersConditionReader' via constructor to bean named 'typeResolver'
2025-08-18 17:34:48.989    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'operationResponseClassReader'
2025-08-18 17:34:48.992    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'operationResponseClassReader' via constructor to bean named 'schemaPluginsManager'
2025-08-18 17:34:48.992    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'operationResponseClassReader' via constructor to bean named 'jacksonEnumTypeDeterminer'
2025-08-18 17:34:48.992    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'operationResponseClassReader' via constructor to bean named 'typeNameExtractor'
2025-08-18 17:34:48.994    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'parameterMultiplesReader'
2025-08-18 17:34:48.998    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'parameterTypeReader'
2025-08-18 17:34:49.002    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'parameterRequiredReader'
2025-08-18 17:34:49.005    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'parameterRequiredReader' via constructor to bean named 'descriptionResolver'
2025-08-18 17:34:49.010    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'parameterDataTypeReader'
2025-08-18 17:34:49.014    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'parameterDataTypeReader' via constructor to bean named 'schemaPluginsManager'
2025-08-18 17:34:49.014    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'parameterDataTypeReader' via constructor to bean named 'typeNameExtractor'
2025-08-18 17:34:49.014    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'parameterDataTypeReader' via constructor to bean named 'jacksonEnumTypeDeterminer'
2025-08-18 17:34:49.014    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'parameterDataTypeReader' via constructor to bean named 'modelSpecificationFactory'
2025-08-18 17:34:49.017    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'parameterDefaultReader'
2025-08-18 17:34:49.019    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'parameterDefaultReader' via constructor to bean named 'descriptionResolver'
2025-08-18 17:34:49.021    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'parameterNameReader'
2025-08-18 17:34:49.025    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'expandedParameterBuilder'
2025-08-18 17:34:49.028    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'expandedParameterBuilder' via constructor to bean named 'typeResolver'
2025-08-18 17:34:49.028    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'expandedParameterBuilder' via constructor to bean named 'jacksonEnumTypeDeterminer'
2025-08-18 17:34:49.031    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'defaultResponseTypeReader'
2025-08-18 17:34:49.035    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'documentationPluginsBootstrapper'
2025-08-18 17:34:49.042    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'webMvcRequestHandlerProvider'
2025-08-18 17:34:49.051    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'methodResolver'
2025-08-18 17:34:49.051    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'methodResolver' via factory method to bean named 'typeResolver'
2025-08-18 17:34:49.063    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'requestMappingHandlerMapping'
2025-08-18 17:34:49.064    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'mvcContentNegotiationManager'
2025-08-18 17:34:49.087    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'mvcConversionService'
2025-08-18 17:34:49.132    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'requestMappingHandlerMapping' via factory method to bean named 'mvcContentNegotiationManager'
2025-08-18 17:34:49.132    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'requestMappingHandlerMapping' via factory method to bean named 'mvcConversionService'
2025-08-18 17:34:49.132    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'requestMappingHandlerMapping' via factory method to bean named 'mvcResourceUrlProvider'
2025-08-18 17:34:49.194    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.sleuth.instrument.web.mvc.SpanCustomizingAsyncHandlerInterceptor'
2025-08-18 17:34:49.326    [main] DEBUG logger_name:_.s.w.s.HandlerMapping.Mappings - message:
	c.b.BizApplication:
	{GET []}: indexs()
2025-08-18 17:34:49.330    [main] DEBUG logger_name:_.s.w.s.HandlerMapping.Mappings - message:
	c.b.a.u.c.UploadController:
	{POST [/common/api/file/upload]}: upload(UploadReqDTO)
	{GET [/upload/file/**]}: download(HttpServletRequest,HttpServletResponse)
2025-08-18 17:34:49.337    [main] DEBUG logger_name:_.s.w.s.HandlerMapping.Mappings - message:
	c.b.b.c.AgreementController:
	{POST [/agreement/getAgreement]}: getAgreement(AgreementReq)
	{POST [/agreement/addAgreement]}: addAgreement(AgreementReq)
	{POST [/agreement/getUserByCode]}: getUserByCode(String)
	{POST [/agreement/getDept]}: getDept()
	{POST [/agreement/fileUpload]}: fileUpload(MultipartFile)
2025-08-18 17:34:49.338    [main] DEBUG logger_name:_.s.w.s.HandlerMapping.Mappings - message:
	c.b.b.c.BaiduApiController:
	{POST [/baiDu/drivingCardUrl]}: drivingCardUrl(IdCardUrlReq)
	{POST [/baiDu/vehiclelicense]}: vehiclelicense(IdCardUrlReq)
	{POST [/baiDu/carNoUrl]}: carNoUrl(IdCardUrlReq)
	{POST [/baiDu/idCardUrl]}: idCardUrl(IdCardUrlReq)
2025-08-18 17:34:49.338    [main] DEBUG logger_name:_.s.w.s.HandlerMapping.Mappings - message:
	c.b.b.c.DriverController:
	{POST [/driver/addDriver]}: addDriver(driver_qua_review)
	{POST [/driver/getDriver]}: getDriver()
2025-08-18 17:34:49.338    [main] DEBUG logger_name:_.s.w.s.HandlerMapping.Mappings - message:
	c.b.b.c.TransportController:
	{POST [/transport/updateFields]}: updateFields(TransportUpdateReq)
2025-08-18 17:34:49.341    [main] DEBUG logger_name:_.s.w.s.HandlerMapping.Mappings - message:
	c.b.m.d.c.DataBoardController:
	{POST [/data/getAddDriverYear]}: getAddDriverYear(DataReq)
	{POST [/data/getDriverAge]}: getDriverAge(DataReq)
	{POST [/data/getMoney]}: getMoney()
	{POST [/data/getHidden]}: getHidden(DataReq)
	{POST [/data/getAccident]}: getAccident(DataReq)
	{POST [/data/getWagesList]}: getWagesList(WagesReq)
	{POST [/data/monthInspection]}: monthInspection(DataReq)
	{POST [/data/onLine]}: onLine(DataReq)
	{POST [/data/carType]}: carType(DataReq)
	{POST [/data/getComplaint]}: getComplaint(DataReq)
	{POST [/data/getChuZuPeople]}: getChuZuPeople()
	{POST [/data/weather]}: weatherNow()
	{POST [/data/getHonor]}: getHonor(DataReq)
	{POST [/data/getSummary]}: getSummary(DataReq)
	{POST [/data/getCarYear]}: getCarYear(DataReq)
	{POST [/data/saveSubscribe]}: saveSubscribe(SubscribeReq)
2025-08-18 17:34:49.342    [main] DEBUG logger_name:_.s.w.s.HandlerMapping.Mappings - message:
	c.b.m.e.c.ExamController:
	{POST [/exam/api/exam/exam/detail]}: find(BaseIdReqDTO)
	{POST [/exam/api/exam/exam/save]}: save(ExamSaveReqDTO)
	{POST [/exam/api/exam/exam/state]}: state(BaseStateReqDTO)
	{POST [/exam/api/exam/exam/delete]}: edit(BaseIdsReqDTO)
	{POST [/exam/api/exam/exam/paging]}: paging(PagingReqDTO)
	{POST [/exam/api/exam/exam/online-paging]}: myPaging(PagingReqDTO)
	{POST [/exam/api/exam/exam/review-paging]}: reviewPaging(PagingReqDTO)
2025-08-18 17:34:49.343    [main] DEBUG logger_name:_.s.w.s.HandlerMapping.Mappings - message:
	c.b.m.h.c.DriverDriController:
	{POST [/dri/add]}: add(DriverInformation)
	{POST [/dri/getOne]}: getList(DriverInformation)
	{POST [/dri/getDeiverAgree]}: getDeiverAgree()
2025-08-18 17:34:49.344    [main] DEBUG logger_name:_.s.w.s.HandlerMapping.Mappings - message:
	c.b.m.p.c.PaperController:
	{POST [/exam/api/paper/paper/detail]}: find(BaseIdReqDTO)
	{POST [/exam/api/paper/paper/save]}: save(PaperDTO)
	{POST [/exam/api/paper/paper/create-paper]}: save(PaperCreateReqDTO)
	{POST [/exam/api/paper/paper/delete]}: edit(BaseIdsReqDTO)
	{POST [/exam/api/paper/paper/paging]}: paging(PagingReqDTO)
	{POST [/exam/api/paper/paper/paper-detail]}: paperDetail(BaseIdReqDTO)
	{POST [/exam/api/paper/paper/check-process]}: checkProcess()
	{POST [/exam/api/paper/paper/hand-exam]}: handleExam(BaseIdReqDTO)
	{POST [/exam/api/paper/paper/paper-result]}: paperResult(BaseIdReqDTO)
	{POST [/exam/api/paper/paper/qu-detail]}: quDetail(PaperQuQueryDTO)
	{POST [/exam/api/paper/paper/fill-answer]}: fillAnswer(PaperAnswerDTO)
2025-08-18 17:34:49.345    [main] DEBUG logger_name:_.s.w.s.HandlerMapping.Mappings - message:
	c.b.m.q.c.QuController:
	{POST [/exam/api/qu/qu/list]}: list(QuDTO)
	{POST [/exam/api/qu/qu/save]}: save(QuDetailDTO)
	{POST [/exam/api/qu/qu/detail]}: detail(BaseIdReqDTO)
	{ [/exam/api/qu/qu/export]}: exportFile(HttpServletResponse,QuQueryReqDTO)
	{ [/exam/api/qu/qu/import/template]}: importFileTemplate(HttpServletResponse)
	{POST [/exam/api/qu/qu/delete]}: edit(BaseIdsReqDTO)
	{POST [/exam/api/qu/qu/paging]}: paging(PagingReqDTO)
	{ [/exam/api/qu/qu/import]}: importFile(MultipartFile)
2025-08-18 17:34:49.346    [main] DEBUG logger_name:_.s.w.s.HandlerMapping.Mappings - message:
	c.b.m.r.c.RepoController:
	{POST [/exam/api/repo/detail]}: find(BaseIdReqDTO)
	{POST [/exam/api/repo/save]}: save(RepoDTO)
	{POST [/exam/api/repo/delete]}: edit(BaseIdsReqDTO)
	{POST [/exam/api/repo/paging]}: paging(PagingReqDTO)
	{POST [/exam/api/repo/batch-action]}: batchAction(QuRepoBatchReqDTO)
2025-08-18 17:34:49.346    [main] DEBUG logger_name:_.s.w.s.HandlerMapping.Mappings - message:
	c.b.m.s.c.c.SysConfigController:
	{POST [/exam/api/sys/config/detail]}: find()
	{POST [/exam/api/sys/config/save]}: save(SysConfigDTO)
2025-08-18 17:34:49.347    [main] DEBUG logger_name:_.s.w.s.HandlerMapping.Mappings - message:
	c.b.m.s.d.c.SysDepartController:
	{POST [/exam/api/sys/depart/detail]}: find(BaseIdReqDTO)
	{POST [/exam/api/sys/depart/list]}: list()
	{POST [/exam/api/sys/depart/save]}: save(SysDepartDTO)
	{POST [/exam/api/sys/depart/sort]}: sort(DepartSortReqDTO)
	{POST [/exam/api/sys/depart/tree]}: tree()
	{POST [/exam/api/sys/depart/delete]}: edit(BaseIdsReqDTO)
	{POST [/exam/api/sys/depart/paging]}: paging(PagingReqDTO)
2025-08-18 17:34:49.347    [main] DEBUG logger_name:_.s.w.s.HandlerMapping.Mappings - message:
	c.b.m.s.u.c.CaptchaController:
	{[GET, POST] [/exam/api/captcha/get]}: captcha(HttpServletRequest,HttpServletResponse)
2025-08-18 17:34:49.351    [main] DEBUG logger_name:_.s.w.s.HandlerMapping.Mappings - message:
	c.b.m.s.u.c.SysRoleController:
	{POST [/exam/api/sys/role/list]}: list()
	{POST [/exam/api/sys/role/paging]}: paging(PagingReqDTO)
2025-08-18 17:34:49.352    [main] DEBUG logger_name:_.s.w.s.HandlerMapping.Mappings - message:
	c.b.m.s.u.c.SysUserController:
	{POST [/exam/api/sys/user/update]}: update(SysUserDTO)
	{POST [/exam/api/sys/user/save]}: save(SysUserSaveReqDTO)
	{POST [/exam/api/sys/user/state]}: state(BaseStateReqDTO)
	{POST [/exam/api/sys/user/info]}: info(String)
	{POST [/exam/api/sys/user/reg]}: reg(SysUserDTO)
	{POST [/exam/api/sys/user/login]}: login(SysUserLoginReqDTO)
	{POST [/exam/api/sys/user/delete]}: edit(BaseIdsReqDTO)
	{POST [/exam/api/sys/user/paging]}: paging(PagingReqDTO)
	{POST [/exam/api/sys/user/logout]}: logout(HttpServletRequest)
	{POST [/exam/api/sys/user/quick-reg]}: quick(SysUserDTO)
	{[GET, POST] [/exam/api/sys/user/get-captcha]}: captcha(HttpServletRequest,HttpServletResponse)
	{POST [/exam/api/sys/user/mongo-login]}: mongoLogin(SysUserLoginReqDTO)
2025-08-18 17:34:49.353    [main] DEBUG logger_name:_.s.w.s.HandlerMapping.Mappings - message:
	c.b.m.u.b.c.UserBookController:
	{POST [/exam/api/user/wrong-book/delete]}: delete(BaseIdsReqDTO)
	{POST [/exam/api/user/wrong-book/paging]}: paging(PagingReqDTO)
	{POST [/exam/api/user/wrong-book/next]}: nextQu(UserBookDTO)
2025-08-18 17:34:49.353    [main] DEBUG logger_name:_.s.w.s.HandlerMapping.Mappings - message:
	c.b.m.u.e.c.UserExamController:
	{POST [/exam/api/user/exam/paging]}: paging(PagingReqDTO)
	{POST [/exam/api/user/exam/my-paging]}: myPaging(PagingReqDTO)
2025-08-18 17:34:49.354    [main] DEBUG logger_name:_.s.w.s.HandlerMapping.Mappings - message:
	c.b.o.c.FileUploadController:
	{GET [/bytes/{bucketName}]}: bytes(String,String)
	{POST [/upload/{bucketName}]}: upload(MultipartFile,String,String,String)
	{POST [/upload/base64/{bucketName}]}: upload(String,String,String,String)
	{GET [/file/link/{bucketName}]}: fileLink(String,String)
	{GET [/largeFile/uploadCertificate/{bucketName}/{filename}/{totalPartNumber}]}: getLargeFileUploadCertificate(String,String,Integer,String)
	{GET [/file/preview/{bucketName}]}: preview(HttpServletResponse,String,String)
	{POST [/file/links]}: filelinks(List)
	{POST [/uploadPart/{bucketName}/{uploadId}/{partNumber}]}: uploadPart(String,MultipartFile,String,String,String,String,Integer)
	{POST [/uploadPart/file/{bucketName}/{uploadId}/{partNumber}]}: uploadPartFile(String,MultipartFile,String,String,String,String,Integer)
2025-08-18 17:34:49.355    [main] DEBUG logger_name:_.s.w.s.HandlerMapping.Mappings - message:
	s.d.s.w.Swagger2ControllerWebMvc:
	{GET [/v2/api-docs], produces [application/json || application/hal+json]}: getDocumentation(String,HttpServletRequest)
2025-08-18 17:34:49.356    [main] DEBUG logger_name:_.s.w.s.HandlerMapping.Mappings - message:
	s.d.s.w.ApiResourceController:
	{GET [/swagger-resources/configuration/ui], produces [application/json]}: uiConfiguration()
	{GET [/swagger-resources], produces [application/json]}: swaggerResources()
	{GET [/swagger-resources/configuration/security], produces [application/json]}: securityConfiguration()
2025-08-18 17:34:49.358    [main] DEBUG logger_name:_.s.w.s.HandlerMapping.Mappings - message:
	s.d.o.w.OpenApiControllerWebMvc:
	{GET [/v3/api-docs], produces [application/json || application/hal+json]}: getDocumentation(String,HttpServletRequest)
2025-08-18 17:34:49.365    [main] DEBUG logger_name:_.s.w.s.HandlerMapping.Mappings - message:
	o.s.b.a.w.s.e.BasicErrorController:
	{ [/error]}: error(HttpServletRequest)
	{ [/error], produces [text/html]}: errorHtml(HttpServletRequest,HttpServletResponse)
2025-08-18 17:34:49.370    [main] DEBUG logger_name:o.s.w.s.m.m.a.RequestMappingHandlerMapping - message:110 mappings in 'requestMappingHandlerMapping'
2025-08-18 17:34:49.397    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'webMvcRequestHandlerProvider' via constructor to bean named 'methodResolver'
2025-08-18 17:34:49.397    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'webMvcRequestHandlerProvider' via constructor to bean named 'requestMappingHandlerMapping'
2025-08-18 17:34:49.403    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'defaults'
2025-08-18 17:34:49.438    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'pathProvider'
2025-08-18 17:34:49.444    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'documentationPluginsBootstrapper' via constructor to bean named 'documentationPluginsManager'
2025-08-18 17:34:49.444    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'documentationPluginsBootstrapper' via constructor to bean named 'webMvcRequestHandlerProvider'
2025-08-18 17:34:49.444    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'documentationPluginsBootstrapper' via constructor to bean named 'resourceGroupCache'
2025-08-18 17:34:49.444    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'documentationPluginsBootstrapper' via constructor to bean named 'apiDocumentationScanner'
2025-08-18 17:34:49.444    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'documentationPluginsBootstrapper' via constructor to bean named 'typeResolver'
2025-08-18 17:34:49.444    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'documentationPluginsBootstrapper' via constructor to bean named 'defaults'
2025-08-18 17:34:49.444    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'documentationPluginsBootstrapper' via constructor to bean named 'pathProvider'
2025-08-18 17:34:49.444    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'documentationPluginsBootstrapper' via constructor to bean named 'environment'
2025-08-18 17:34:49.460    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'queryStringUriTemplateDecorator'
2025-08-18 17:34:49.465    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'pathMappingDecorator'
2025-08-18 17:34:49.468    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'pathSanitizer'
2025-08-18 17:34:49.472    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'operationPathDecorator'
2025-08-18 17:34:49.476    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'jacksonJsonViewProvider'
2025-08-18 17:34:49.478    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'jacksonJsonViewProvider' via constructor to bean named 'typeResolver'
2025-08-18 17:34:49.481    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'propertyDiscriminatorBasedInheritancePlugin'
2025-08-18 17:34:49.483    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'propertyDiscriminatorBasedInheritancePlugin' via constructor to bean named 'typeResolver'
2025-08-18 17:34:49.483    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'propertyDiscriminatorBasedInheritancePlugin' via constructor to bean named 'jacksonEnumTypeDeterminer'
2025-08-18 17:34:49.483    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'propertyDiscriminatorBasedInheritancePlugin' via constructor to bean named 'typeNameExtractor'
2025-08-18 17:34:49.483    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'propertyDiscriminatorBasedInheritancePlugin' via constructor to bean named 'modelSpecificationFactory'
2025-08-18 17:34:49.485    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'xmlModelPlugin'
2025-08-18 17:34:49.488    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'xmlModelPlugin' via constructor to bean named 'typeResolver'
2025-08-18 17:34:49.490    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'jsonIgnorePropertiesModelPlugin'
2025-08-18 17:34:49.493    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'jsonIgnorePropertiesModelPlugin' via constructor to bean named 'typeResolver'
2025-08-18 17:34:49.495    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'xmlPropertyPlugin'
2025-08-18 17:34:49.500    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'openApiControllerWebMvc'
2025-08-18 17:34:49.502    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'serviceModelToOpenApiMapperImpl'
2025-08-18 17:34:49.517    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'oasVendorExtensionsMapperImpl'
2025-08-18 17:34:49.524    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'oasLicenceMapper'
2025-08-18 17:34:49.529    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'examplesMapperImpl'
2025-08-18 17:34:49.536    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'oasSecurityMapperImpl'
2025-08-18 17:34:49.544    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'schemaMapperImpl'
2025-08-18 17:34:49.559    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'styleEnumMapperImpl'
2025-08-18 17:34:49.565    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'securitySchemeMapperImpl'
2025-08-18 17:34:49.581    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'webMvcOpenApiTransformationFilterRegistry'
2025-08-18 17:34:49.586    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'openApiControllerWebMvc' via constructor to bean named 'resourceGroupCache'
2025-08-18 17:34:49.586    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'openApiControllerWebMvc' via constructor to bean named 'serviceModelToOpenApiMapperImpl'
2025-08-18 17:34:49.586    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'openApiControllerWebMvc' via constructor to bean named 'jsonSerializer'
2025-08-18 17:34:49.586    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'openApiControllerWebMvc' via constructor to bean named 'webMvcOpenApiTransformationFilterRegistry'
2025-08-18 17:34:49.588    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'lbRestTemplate'
2025-08-18 17:34:49.590    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'loadBalancerInterceptor'
2025-08-18 17:34:49.590    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$RetryInterceptorAutoConfiguration'
2025-08-18 17:34:49.595    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'loadBalancerRequestFactory'
2025-08-18 17:34:49.595    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration'
2025-08-18 17:34:49.597    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'lbRestTemplate'
2025-08-18 17:34:49.609    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'tracingClientHttpRequestInterceptor'
2025-08-18 17:34:49.609    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.sleuth.autoconfig.instrument.web.client.TraceWebClientAutoConfiguration$RestTemplateConfig'
2025-08-18 17:34:49.614    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'braveHttpClientHandler'
2025-08-18 17:34:49.614    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.sleuth.autoconfig.brave.instrument.web.BraveHttpBridgeConfiguration'
2025-08-18 17:34:49.618    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'httpTracing'
2025-08-18 17:34:49.618    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.sleuth.autoconfig.brave.instrument.web.BraveHttpConfiguration'
2025-08-18 17:34:49.628    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'sleuthSkipPatternProvider'
2025-08-18 17:34:49.628    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.sleuth.autoconfig.instrument.web.SkipPatternConfiguration'
2025-08-18 17:34:49.633    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'defaultSkipPatternBean'
2025-08-18 17:34:49.633    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.sleuth.autoconfig.instrument.web.SkipPatternConfiguration$DefaultSkipPatternConfig'
2025-08-18 17:34:49.637    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'defaultSkipPatternBean' via factory method to bean named 'environment'
2025-08-18 17:34:49.637    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'defaultSkipPatternBean' via factory method to bean named 'spring.sleuth.web-org.springframework.cloud.sleuth.autoconfig.instrument.web.SleuthWebProperties'
2025-08-18 17:34:49.652    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'sleuthSkipPatternProvider' via factory method to bean named 'defaultSkipPatternBean'
2025-08-18 17:34:49.670    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'httpTracing' via factory method to bean named 'tracing'
2025-08-18 17:34:49.670    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'httpTracing' via factory method to bean named 'sleuthSkipPatternProvider'
2025-08-18 17:34:49.670    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'httpTracing' via factory method to bean named 'org.springframework.beans.factory.support.DefaultListableBeanFactory@5e230fc6'
2025-08-18 17:34:49.670    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'sleuthHttpClientSampler'
2025-08-18 17:34:49.670    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'sleuthHttpClientSampler' via factory method to bean named 'spring.sleuth.web-org.springframework.cloud.sleuth.autoconfig.instrument.web.SleuthWebProperties'
2025-08-18 17:34:49.707    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'braveHttpClientHandler' via factory method to bean named 'httpTracing'
2025-08-18 17:34:49.728    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'tracingClientHttpRequestInterceptor' via factory method to bean named 'braveCurrentTraceContext'
2025-08-18 17:34:49.728    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'tracingClientHttpRequestInterceptor' via factory method to bean named 'braveHttpClientHandler'
2025-08-18 17:34:49.816    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.loadbalancer.annotation.LoadBalancerClientConfiguration$BlockingSupportConfiguration'
2025-08-18 17:34:49.820    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'serviceInstanceLoadBalancer'
2025-08-18 17:34:49.822    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'serviceInstanceLoadBalancer' via factory method to bean named 'environment'
2025-08-18 17:34:49.822    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'serviceInstanceLoadBalancer' via factory method to bean named 'loadBalancerClientFactory'
2025-08-18 17:34:49.849    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'mvcPatternParser'
2025-08-18 17:34:49.851    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'mvcUrlPathHelper'
2025-08-18 17:34:49.855    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'mvcPathMatcher'
2025-08-18 17:34:49.859    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'viewControllerHandlerMapping'
2025-08-18 17:34:49.861    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'viewControllerHandlerMapping' via factory method to bean named 'mvcConversionService'
2025-08-18 17:34:49.861    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'viewControllerHandlerMapping' via factory method to bean named 'mvcResourceUrlProvider'
2025-08-18 17:34:49.884    [main] DEBUG logger_name:_.s.w.s.HandlerMapping.Mappings - message:'viewControllerHandlerMapping' {/swagger-ui/=ParameterizableViewController [view="forward:/swagger-ui/index.html"]}
2025-08-18 17:34:49.896    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'beanNameHandlerMapping'
2025-08-18 17:34:49.896    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'beanNameHandlerMapping' via factory method to bean named 'mvcConversionService'
2025-08-18 17:34:49.896    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'beanNameHandlerMapping' via factory method to bean named 'mvcResourceUrlProvider'
2025-08-18 17:34:49.902    [main] DEBUG logger_name:_.s.w.s.HandlerMapping.Mappings - message:'beanNameHandlerMapping' {}
2025-08-18 17:34:49.913    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'routerFunctionMapping'
2025-08-18 17:34:49.913    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'routerFunctionMapping' via factory method to bean named 'mvcConversionService'
2025-08-18 17:34:49.913    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'routerFunctionMapping' via factory method to bean named 'mvcResourceUrlProvider'
2025-08-18 17:34:50.180    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'resourceHandlerMapping'
2025-08-18 17:34:50.181    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'resourceHandlerMapping' via factory method to bean named 'mvcContentNegotiationManager'
2025-08-18 17:34:50.181    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'resourceHandlerMapping' via factory method to bean named 'mvcConversionService'
2025-08-18 17:34:50.181    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'resourceHandlerMapping' via factory method to bean named 'mvcResourceUrlProvider'
2025-08-18 17:34:50.213    [main] DEBUG logger_name:_.s.w.s.HandlerMapping.Mappings - message:'resourceHandlerMapping' {/swagger-ui/**=ResourceHttpRequestHandler [classpath [META-INF/resources/webjars/springfox-swagger-ui/]]}
2025-08-18 17:34:50.219    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'defaultServletHandlerMapping'
2025-08-18 17:34:50.225    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'requestMappingHandlerAdapter'
2025-08-18 17:34:50.226    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'mvcValidator'
2025-08-18 17:34:50.233    [main] DEBUG logger_name:o.h.v.m.ResourceBundleMessageInterpolator - message:Loaded expression factory via original TCCL
2025-08-18 17:34:50.233    [main] DEBUG logger_name:o.h.v.i.e.AbstractConfigurationImpl - message:Setting custom MessageInterpolator of type org.springframework.validation.beanvalidation.LocaleContextMessageInterpolator
2025-08-18 17:34:50.236    [main] DEBUG logger_name:o.h.v.i.e.AbstractConfigurationImpl - message:Setting custom ConstraintValidatorFactory of type org.springframework.validation.beanvalidation.SpringConstraintValidatorFactory
2025-08-18 17:34:50.240    [main] DEBUG logger_name:o.h.v.i.e.AbstractConfigurationImpl - message:Setting custom ParameterNameProvider of type org.springframework.validation.beanvalidation.LocalValidatorFactoryBean$1
2025-08-18 17:34:50.242    [main] DEBUG logger_name:o.h.v.i.x.config.ValidationXmlParser - message:Trying to load META-INF/validation.xml for XML based Validator configuration.
2025-08-18 17:34:50.242    [main] DEBUG logger_name:o.h.v.i.x.c.ResourceLoaderHelper - message:Trying to load META-INF/validation.xml via user class loader
2025-08-18 17:34:50.242    [main] DEBUG logger_name:o.h.v.i.x.c.ResourceLoaderHelper - message:Trying to load META-INF/validation.xml via TCCL
2025-08-18 17:34:50.243    [main] DEBUG logger_name:o.h.v.i.x.c.ResourceLoaderHelper - message:Trying to load META-INF/validation.xml via Hibernate Validator's class loader
2025-08-18 17:34:50.243    [main] DEBUG logger_name:o.h.v.i.x.config.ValidationXmlParser - message:No META-INF/validation.xml found. Using annotation based configuration only.
2025-08-18 17:34:50.245    [main] DEBUG logger_name:o.h.v.i.e.r.TraversableResolvers - message:Cannot find javax.persistence.Persistence on classpath. Assuming non JPA 2 environment. All properties will per default be traversable.
2025-08-18 17:34:50.246    [main] DEBUG logger_name:o.h.v.i.e.ValidatorFactoryConfigurationHelper - message:HV000252: Using org.hibernate.validator.internal.engine.DefaultPropertyNodeNameProvider as property node name provider.
2025-08-18 17:34:50.247    [main] DEBUG logger_name:o.h.v.i.e.ValidatorFactoryConfigurationHelper - message:HV000234: Using org.springframework.validation.beanvalidation.LocaleContextMessageInterpolator as ValidatorFactory-scoped message interpolator.
2025-08-18 17:34:50.247    [main] DEBUG logger_name:o.h.v.i.e.ValidatorFactoryConfigurationHelper - message:HV000234: Using org.hibernate.validator.internal.engine.resolver.TraverseAllTraversableResolver as ValidatorFactory-scoped traversable resolver.
2025-08-18 17:34:50.247    [main] DEBUG logger_name:o.h.v.i.e.ValidatorFactoryConfigurationHelper - message:HV000234: Using org.hibernate.validator.internal.util.ExecutableParameterNameProvider as ValidatorFactory-scoped parameter name provider.
2025-08-18 17:34:50.247    [main] DEBUG logger_name:o.h.v.i.e.ValidatorFactoryConfigurationHelper - message:HV000234: Using org.hibernate.validator.internal.engine.DefaultClockProvider as ValidatorFactory-scoped clock provider.
2025-08-18 17:34:50.247    [main] DEBUG logger_name:o.h.v.i.e.ValidatorFactoryConfigurationHelper - message:HV000234: Using org.hibernate.validator.internal.engine.scripting.DefaultScriptEvaluatorFactory as ValidatorFactory-scoped script evaluator factory.
2025-08-18 17:34:50.270    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'requestMappingHandlerAdapter' via factory method to bean named 'mvcContentNegotiationManager'
2025-08-18 17:34:50.270    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'requestMappingHandlerAdapter' via factory method to bean named 'mvcConversionService'
2025-08-18 17:34:50.270    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'requestMappingHandlerAdapter' via factory method to bean named 'mvcValidator'
2025-08-18 17:34:50.311    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'sortResolver'
2025-08-18 17:34:50.327    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'sortCustomizer'
2025-08-18 17:34:50.327    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.data.web.SpringDataWebAutoConfiguration'
2025-08-18 17:34:50.329    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'org.springframework.boot.autoconfigure.data.web.SpringDataWebAutoConfiguration' via constructor to bean named 'spring.data.web-org.springframework.boot.autoconfigure.data.web.SpringDataWebProperties'
2025-08-18 17:34:50.356    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'pageableResolver'
2025-08-18 17:34:50.370    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'pageableCustomizer'
2025-08-18 17:34:50.444    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'cn.bctools.web.config.UnifyExceptionHandler'
2025-08-18 17:34:50.453    [main] DEBUG logger_name:o.s.w.s.m.m.a.RequestMappingHandlerAdapter - message:ControllerAdvice beans: 1 @ModelAttribute, 1 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-08-18 17:34:50.600    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'handlerFunctionAdapter'
2025-08-18 17:34:50.605    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'mvcUriComponentsContributor'
2025-08-18 17:34:50.607    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'mvcUriComponentsContributor' via factory method to bean named 'mvcConversionService'
2025-08-18 17:34:50.607    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'mvcUriComponentsContributor' via factory method to bean named 'requestMappingHandlerAdapter'
2025-08-18 17:34:50.610    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'httpRequestHandlerAdapter'
2025-08-18 17:34:50.614    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'simpleControllerHandlerAdapter'
2025-08-18 17:34:50.617    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'handlerExceptionResolver'
2025-08-18 17:34:50.617    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'handlerExceptionResolver' via factory method to bean named 'mvcContentNegotiationManager'
2025-08-18 17:34:50.636    [main] DEBUG logger_name:o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - message:ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-08-18 17:34:50.648    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'mvcViewResolver'
2025-08-18 17:34:50.648    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'mvcViewResolver' via factory method to bean named 'mvcContentNegotiationManager'
2025-08-18 17:34:50.661    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'localeResolver'
2025-08-18 17:34:50.666    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'themeResolver'
2025-08-18 17:34:50.672    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'flashMapManager'
2025-08-18 17:34:50.682    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'viewNameTranslator'
2025-08-18 17:34:50.688    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.client.serviceregistry.AutoServiceRegistrationConfiguration'
2025-08-18 17:34:50.692    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'spring.cloud.service-registry.auto-registration-org.springframework.cloud.client.serviceregistry.AutoServiceRegistrationProperties'
2025-08-18 17:34:50.696    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.context.PropertyPlaceholderAutoConfiguration'
2025-08-18 17:34:50.698    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration'
2025-08-18 17:34:50.701    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration'
2025-08-18 17:34:50.705    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration'
2025-08-18 17:34:50.708    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'taskExecutorBuilder'
2025-08-18 17:34:50.711    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'spring.task.execution-org.springframework.boot.autoconfigure.task.TaskExecutionProperties'
2025-08-18 17:34:50.718    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'taskExecutorBuilder' via factory method to bean named 'spring.task.execution-org.springframework.boot.autoconfigure.task.TaskExecutionProperties'
2025-08-18 17:34:50.728    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.validation.ValidationAutoConfiguration'
2025-08-18 17:34:50.731    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'defaultValidator'
2025-08-18 17:34:50.732    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'defaultValidator' via factory method to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@4e6f2bb5'
2025-08-18 17:34:50.735    [main] DEBUG logger_name:o.h.v.m.ResourceBundleMessageInterpolator - message:Loaded expression factory via original TCCL
2025-08-18 17:34:50.738    [main] DEBUG logger_name:o.h.v.i.e.AbstractConfigurationImpl - message:Setting custom MessageInterpolator of type org.springframework.validation.beanvalidation.LocaleContextMessageInterpolator
2025-08-18 17:34:50.738    [main] DEBUG logger_name:o.h.v.i.e.AbstractConfigurationImpl - message:Setting custom ConstraintValidatorFactory of type org.springframework.validation.beanvalidation.SpringConstraintValidatorFactory
2025-08-18 17:34:50.738    [main] DEBUG logger_name:o.h.v.i.e.AbstractConfigurationImpl - message:Setting custom ParameterNameProvider of type org.springframework.validation.beanvalidation.LocalValidatorFactoryBean$1
2025-08-18 17:34:50.739    [main] DEBUG logger_name:o.h.v.i.x.config.ValidationXmlParser - message:Trying to load META-INF/validation.xml for XML based Validator configuration.
2025-08-18 17:34:50.739    [main] DEBUG logger_name:o.h.v.i.x.c.ResourceLoaderHelper - message:Trying to load META-INF/validation.xml via user class loader
2025-08-18 17:34:50.740    [main] DEBUG logger_name:o.h.v.i.x.c.ResourceLoaderHelper - message:Trying to load META-INF/validation.xml via TCCL
2025-08-18 17:34:50.740    [main] DEBUG logger_name:o.h.v.i.x.c.ResourceLoaderHelper - message:Trying to load META-INF/validation.xml via Hibernate Validator's class loader
2025-08-18 17:34:50.741    [main] DEBUG logger_name:o.h.v.i.x.config.ValidationXmlParser - message:No META-INF/validation.xml found. Using annotation based configuration only.
2025-08-18 17:34:50.742    [main] DEBUG logger_name:o.h.v.i.e.r.TraversableResolvers - message:Cannot find javax.persistence.Persistence on classpath. Assuming non JPA 2 environment. All properties will per default be traversable.
2025-08-18 17:34:50.744    [main] DEBUG logger_name:o.h.v.i.e.ValidatorFactoryConfigurationHelper - message:HV000252: Using org.hibernate.validator.internal.engine.DefaultPropertyNodeNameProvider as property node name provider.
2025-08-18 17:34:50.744    [main] DEBUG logger_name:o.h.v.i.e.ValidatorFactoryConfigurationHelper - message:HV000234: Using org.springframework.validation.beanvalidation.LocaleContextMessageInterpolator as ValidatorFactory-scoped message interpolator.
2025-08-18 17:34:50.744    [main] DEBUG logger_name:o.h.v.i.e.ValidatorFactoryConfigurationHelper - message:HV000234: Using org.hibernate.validator.internal.engine.resolver.TraverseAllTraversableResolver as ValidatorFactory-scoped traversable resolver.
2025-08-18 17:34:50.744    [main] DEBUG logger_name:o.h.v.i.e.ValidatorFactoryConfigurationHelper - message:HV000234: Using org.hibernate.validator.internal.util.ExecutableParameterNameProvider as ValidatorFactory-scoped parameter name provider.
2025-08-18 17:34:50.744    [main] DEBUG logger_name:o.h.v.i.e.ValidatorFactoryConfigurationHelper - message:HV000234: Using org.hibernate.validator.internal.engine.DefaultClockProvider as ValidatorFactory-scoped clock provider.
2025-08-18 17:34:50.744    [main] DEBUG logger_name:o.h.v.i.e.ValidatorFactoryConfigurationHelper - message:HV000234: Using org.hibernate.validator.internal.engine.scripting.DefaultScriptEvaluatorFactory as ValidatorFactory-scoped script evaluator factory.
2025-08-18 17:34:50.751    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$WhitelabelErrorViewConfiguration'
2025-08-18 17:34:50.758    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'error'
2025-08-18 17:34:50.760    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'beanNameViewResolver'
2025-08-18 17:34:50.765    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration'
2025-08-18 17:34:50.767    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'spring.web-org.springframework.boot.autoconfigure.web.WebProperties'
2025-08-18 17:34:50.787    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration' via constructor to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@4e6f2bb5'
2025-08-18 17:34:50.787    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration' via constructor to bean named 'spring.web-org.springframework.boot.autoconfigure.web.WebProperties'
2025-08-18 17:34:50.790    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'conventionErrorViewResolver'
2025-08-18 17:34:50.795    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'errorAttributes'
2025-08-18 17:34:50.802    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'basicErrorController'
2025-08-18 17:34:50.803    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'basicErrorController' via factory method to bean named 'errorAttributes'
2025-08-18 17:34:50.810    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'dataSourceInit'
2025-08-18 17:34:50.810    [main] INFO  logger_name:c.b.database.config.DatabaseConfig - message:[mysql-data] 加载单数据源初始化类: cn.bctools.database.init.DataSourceInit
2025-08-18 17:34:50.818    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'tableInfoMapper'
2025-08-18 17:34:51.344    [main] INFO  logger_name:c.b.database.init.DataSourceInit - message:[mysql-data] 加载单个数据源
2025-08-18 17:34:51.346    [main] INFO  logger_name:c.b.database.init.DataSourceInit - message:数据源信息初始化 >>>> 
2025-08-18 17:34:51.423    [main] DEBUG logger_name:org.mybatis.spring.SqlSessionUtils - message:Creating a new SqlSession
2025-08-18 17:34:51.458    [main] DEBUG logger_name:org.mybatis.spring.SqlSessionUtils - message:SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@e8fabad] was not registered for synchronization because synchronization is not active
2025-08-18 17:34:51.565    [main] DEBUG logger_name:c.b.m.e.p.i.TenantLineInnerInterceptor - message:original SQL: SELECT t.TABLE_NAME AS table_name,c.COLUMN_NAME AS field_name,t.TABLE_SCHEMA AS table_schema FROM information_schema.TABLES t INNER JOIN information_schema.COLUMNS c ON t.TABLE_SCHEMA=c.TABLE_SCHEMA AND t.TABLE_NAME=c.TABLE_NAME WHERE t.TABLE_TYPE='BASE TABLE' AND t.TABLE_SCHEMA='jvs'
2025-08-18 17:34:51.854    [main] DEBUG logger_name:c.b.m.e.p.i.TenantLineInnerInterceptor - message:SQL to parse, SQL: SELECT t.TABLE_NAME AS table_name,c.COLUMN_NAME AS field_name,t.TABLE_SCHEMA AS table_schema FROM information_schema.TABLES t INNER JOIN information_schema.COLUMNS c ON t.TABLE_SCHEMA=c.TABLE_SCHEMA AND t.TABLE_NAME=c.TABLE_NAME WHERE t.TABLE_TYPE='BASE TABLE' AND t.TABLE_SCHEMA='jvs'
2025-08-18 17:34:51.868    [main] DEBUG logger_name:c.b.m.e.p.i.TenantLineInnerInterceptor - message:parse the finished SQL: SELECT t.TABLE_NAME AS table_name, c.COLUMN_NAME AS field_name, t.TABLE_SCHEMA AS table_schema FROM information_schema.TABLES t INNER JOIN information_schema.COLUMNS c ON t.TABLE_SCHEMA = c.TABLE_SCHEMA AND t.TABLE_NAME = c.TABLE_NAME WHERE t.TABLE_TYPE = 'BASE TABLE' AND t.TABLE_SCHEMA = 'jvs'
2025-08-18 17:34:51.906    [main] DEBUG logger_name:o.s.jdbc.datasource.DataSourceUtils - message:Fetching JDBC Connection from DataSource
2025-08-18 17:34:51.907    [main] DEBUG logger_name:o.m.s.t.SpringManagedTransaction - message:JDBC Connection [HikariProxyConnection@1052117080 wrapping com.mysql.cj.jdbc.ConnectionImpl@469f8de2] will not be managed by Spring
2025-08-18 17:34:52.278    [main] DEBUG logger_name:org.mybatis.spring.SqlSessionUtils - message:Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@e8fabad]
2025-08-18 17:34:52.279    [main] INFO  logger_name:c.b.database.init.DataSourceInit - message:>>>> 数据表加载完毕, 共0个表字段
2025-08-18 17:34:52.279    [main] INFO  logger_name:c.b.database.init.DataSourceInit - message:>>>> 数据源加载完毕, 共1个数据源
2025-08-18 17:34:52.283    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceJmxConfiguration$Hikari'
2025-08-18 17:34:52.286    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'org.springframework.boot.autoconfigure.jdbc.DataSourceJmxConfiguration$Hikari' via constructor to bean named 'dataSource'
2025-08-18 17:34:52.292    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceJmxConfiguration'
2025-08-18 17:34:52.295    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration$PooledDataSourceConfiguration'
2025-08-18 17:34:52.298    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration$HikariPoolDataSourceMetadataProviderConfiguration'
2025-08-18 17:34:52.300    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'hikariPoolDataSourceMetadataProvider'
2025-08-18 17:34:52.315    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration'
2025-08-18 17:34:52.317    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration'
2025-08-18 17:34:52.320    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'cn.bctools.database.property.SqlProperties'
2025-08-18 17:34:52.363    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'springfox.documentation.swagger.configuration.SwaggerCommonConfiguration'
2025-08-18 17:34:52.365    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'springfox.documentation.spring.web.SpringfoxWebMvcConfiguration'
2025-08-18 17:34:52.369    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'springfox.documentation.swagger2.configuration.Swagger2WebMvcConfiguration'
2025-08-18 17:34:52.373    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'webMvcSwaggerTransformer'
2025-08-18 17:34:52.373    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'webMvcSwaggerTransformer' via factory method to bean named 'environment'
2025-08-18 17:34:52.383    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'cn.bctools.gray.config.GrayConfig'
2025-08-18 17:34:52.385    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'cn.bctools.log.aspect.SysLogAspect'
2025-08-18 17:34:52.386    [main] DEBUG logger_name:o.s.c.LocalVariableTableParameterNameDiscoverer - message:Cannot find '.class' file for class [class cn.bctools.log.aspect.SysLogAspect$$EnhancerBySpringCGLIB$$4fc20f1] - unable to determine constructor/method parameter names
2025-08-18 17:34:52.388    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'rabbitTemplate'
2025-08-18 17:34:52.388    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'cn.bctools.rabbit.config.MyRabbitConfig'
2025-08-18 17:34:52.395    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'rabbitConnectionFactory'
2025-08-18 17:34:52.395    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration$RabbitConnectionFactoryCreator'
2025-08-18 17:34:52.399    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'rabbitConnectionFactoryBeanConfigurer'
2025-08-18 17:34:52.401    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'spring.rabbitmq-org.springframework.boot.autoconfigure.amqp.RabbitProperties'
2025-08-18 17:34:52.452    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'rabbitConnectionFactoryBeanConfigurer' via factory method to bean named 'spring.rabbitmq-org.springframework.boot.autoconfigure.amqp.RabbitProperties'
2025-08-18 17:34:52.452    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'rabbitConnectionFactoryBeanConfigurer' via factory method to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@4e6f2bb5'
2025-08-18 17:34:52.461    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'rabbitConnectionFactoryConfigurer'
2025-08-18 17:34:52.463    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'rabbitConnectionFactoryConfigurer' via factory method to bean named 'spring.rabbitmq-org.springframework.boot.autoconfigure.amqp.RabbitProperties'
2025-08-18 17:34:52.469    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'rabbitConnectionFactory' via factory method to bean named 'rabbitConnectionFactoryBeanConfigurer'
2025-08-18 17:34:52.469    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'rabbitConnectionFactory' via factory method to bean named 'rabbitConnectionFactoryConfigurer'
2025-08-18 17:34:52.726    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'messageConverter'
2025-08-18 17:34:52.765    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'rabbitTemplate' via factory method to bean named 'rabbitConnectionFactory'
2025-08-18 17:34:52.765    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'rabbitTemplate' via factory method to bean named 'messageConverter'
2025-08-18 17:34:52.842    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'springRabbitTracing'
2025-08-18 17:34:52.842    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.sleuth.autoconfig.brave.instrument.messaging.BraveMessagingAutoConfiguration$SleuthRabbitConfiguration'
2025-08-18 17:34:52.847    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'messagingTracing'
2025-08-18 17:34:52.847    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.sleuth.autoconfig.brave.instrument.messaging.BraveMessagingAutoConfiguration'
2025-08-18 17:34:52.859    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'messagingTracing' via factory method to bean named 'tracing'
2025-08-18 17:34:52.869    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'spring.sleuth.messaging-org.springframework.cloud.sleuth.autoconfig.instrument.messaging.SleuthMessagingProperties'
2025-08-18 17:34:52.879    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'springRabbitTracing' via factory method to bean named 'messagingTracing'
2025-08-18 17:34:52.879    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'springRabbitTracing' via factory method to bean named 'spring.sleuth.messaging-org.springframework.cloud.sleuth.autoconfig.instrument.messaging.SleuthMessagingProperties'
2025-08-18 17:34:53.013    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'cn.bctools.log.aspect.SysLogAspect' via constructor to bean named 'braveTracer'
2025-08-18 17:34:53.013    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'cn.bctools.log.aspect.SysLogAspect' via constructor to bean named 'rabbitTemplate'
2025-08-18 17:34:53.014    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'cn.bctools.log.config.LogMqConfig'
2025-08-18 17:34:53.018    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'sysLogSaveExchange'
2025-08-18 17:34:53.037    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'sysLogSaveQueue'
2025-08-18 17:34:53.053    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'sysLogSaveBinding'
2025-08-18 17:34:53.068    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'rabbitAdmin'
2025-08-18 17:34:53.068    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'rabbitAdmin' via factory method to bean named 'rabbitConnectionFactory'
2025-08-18 17:34:53.151    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'batchQueueRabbitListenerContainerFactory'
2025-08-18 17:34:53.151    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'batchQueueRabbitListenerContainerFactory' via factory method to bean named 'rabbitConnectionFactory'
2025-08-18 17:34:53.180    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'redisKeySerializer'
2025-08-18 17:34:53.187    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'redisMessageListenerContainer'
2025-08-18 17:34:53.192    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'redisMessageListenerContainer' via factory method to bean named 'redisConnectionFactory'
2025-08-18 17:34:53.251    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'jvsCacheConvert'
2025-08-18 17:34:53.254    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'com.alibaba.cloud.nacos.NacosConfigAutoConfiguration'
2025-08-18 17:34:53.259    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'nacosConfigProperties'
2025-08-18 17:34:53.259    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'nacosConfigProperties' via factory method to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@4e6f2bb5'
2025-08-18 17:34:53.271    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'nacosRefreshHistory'
2025-08-18 17:34:53.282    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'nacosConfigManager'
2025-08-18 17:34:53.283    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'nacosConfigManager' via factory method to bean named 'nacosConfigProperties'
2025-08-18 17:34:53.286    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'nacosContextRefresher'
2025-08-18 17:34:53.289    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'nacosContextRefresher' via factory method to bean named 'nacosConfigManager'
2025-08-18 17:34:53.289    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'nacosContextRefresher' via factory method to bean named 'nacosRefreshHistory'
2025-08-18 17:34:53.296    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'nacosServiceDiscovery'
2025-08-18 17:34:53.297    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'nacosServiceDiscovery' via factory method to bean named 'nacosProperties'
2025-08-18 17:34:53.297    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'nacosServiceDiscovery' via factory method to bean named 'nacosServiceManager'
2025-08-18 17:34:53.304    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'com.alibaba.cloud.nacos.discovery.NacosDiscoveryClientConfiguration'
2025-08-18 17:34:53.309    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'nacosDiscoveryClient'
2025-08-18 17:34:53.310    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'nacosDiscoveryClient' via factory method to bean named 'nacosServiceDiscovery'
2025-08-18 17:34:53.317    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.client.serviceregistry.AutoServiceRegistrationAutoConfiguration'
2025-08-18 17:34:53.320    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'nacosAutoServiceRegistration'
2025-08-18 17:34:53.323    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'nacosServiceRegistry'
2025-08-18 17:34:53.323    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'nacosServiceRegistry' via factory method to bean named 'nacosServiceManager'
2025-08-18 17:34:53.323    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'nacosServiceRegistry' via factory method to bean named 'nacosProperties'
2025-08-18 17:34:53.331    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'nacosAutoServiceRegistration' via factory method to bean named 'nacosServiceRegistry'
2025-08-18 17:34:53.331    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'nacosAutoServiceRegistration' via factory method to bean named 'spring.cloud.service-registry.auto-registration-org.springframework.cloud.client.serviceregistry.AutoServiceRegistrationProperties'
2025-08-18 17:34:53.331    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'nacosAutoServiceRegistration' via factory method to bean named 'nacosRegistration'
2025-08-18 17:34:53.359    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'ddlApplicationRunner'
2025-08-18 17:34:53.362    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'com.cool.request.components.ComponentLoader'
2025-08-18 17:34:53.381    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.apache.shiro.spring.boot.autoconfigure.ShiroAnnotationProcessorAutoConfiguration'
2025-08-18 17:34:53.386    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.apache.shiro.spring.web.config.ShiroRequestMappingConfig'
2025-08-18 17:34:53.387    [main] DEBUG logger_name:o.s.c.LocalVariableTableParameterNameDiscoverer - message:Cannot find '.class' file for class [class org.apache.shiro.spring.web.config.ShiroRequestMappingConfig$$EnhancerBySpringCGLIB$$968a951b] - unable to determine constructor/method parameter names
2025-08-18 17:34:53.388    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'org.apache.shiro.spring.web.config.ShiroRequestMappingConfig' via constructor to bean named 'requestMappingHandlerMapping'
2025-08-18 17:34:53.393    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.apache.shiro.spring.config.web.autoconfigure.ShiroWebMvcAutoConfiguration'
2025-08-18 17:34:53.396    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'authenticationStrategy'
2025-08-18 17:34:53.401    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'subjectDAO'
2025-08-18 17:34:53.401    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'sessionStorageEvaluator'
2025-08-18 17:34:53.410    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'subjectFactory'
2025-08-18 17:34:53.413    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'sessionFactory'
2025-08-18 17:34:53.415    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'sessionDAO'
2025-08-18 17:34:53.420    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'sessionCookieTemplate'
2025-08-18 17:34:53.427    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'rememberMeManager'
2025-08-18 17:34:53.427    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'rememberMeCookieTemplate'
2025-08-18 17:34:53.437    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'shiroUrlPathHelper'
2025-08-18 17:34:53.442    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.apache.shiro.spring.boot.autoconfigure.ShiroAutoConfiguration'
2025-08-18 17:34:53.454    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'globalFilters'
2025-08-18 17:34:53.463    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration$RabbitTemplateConfiguration'
2025-08-18 17:34:53.466    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'rabbitTemplateConfigurer'
2025-08-18 17:34:53.467    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'rabbitTemplateConfigurer' via factory method to bean named 'spring.rabbitmq-org.springframework.boot.autoconfigure.amqp.RabbitProperties'
2025-08-18 17:34:53.475    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration$MessagingTemplateConfiguration'
2025-08-18 17:34:53.477    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'rabbitMessagingTemplate'
2025-08-18 17:34:53.477    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'rabbitMessagingTemplate' via factory method to bean named 'rabbitTemplate'
2025-08-18 17:34:53.527    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.amqp.RabbitAnnotationDrivenConfiguration'
2025-08-18 17:34:53.528    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'org.springframework.boot.autoconfigure.amqp.RabbitAnnotationDrivenConfiguration' via constructor to bean named 'spring.rabbitmq-org.springframework.boot.autoconfigure.amqp.RabbitProperties'
2025-08-18 17:34:53.531    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'simpleRabbitListenerContainerFactoryConfigurer'
2025-08-18 17:34:53.542    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'rabbitListenerContainerFactory'
2025-08-18 17:34:53.543    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'rabbitListenerContainerFactory' via factory method to bean named 'simpleRabbitListenerContainerFactoryConfigurer'
2025-08-18 17:34:53.543    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'rabbitListenerContainerFactory' via factory method to bean named 'rabbitConnectionFactory'
2025-08-18 17:34:53.550    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'directRabbitListenerContainerFactoryConfigurer'
2025-08-18 17:34:53.556    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration'
2025-08-18 17:34:53.560    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.aop.AopAutoConfiguration$AspectJAutoProxyingConfiguration$CglibAutoProxyConfiguration'
2025-08-18 17:34:53.563    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.aop.AopAutoConfiguration$AspectJAutoProxyingConfiguration'
2025-08-18 17:34:53.566    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.aop.AopAutoConfiguration'
2025-08-18 17:34:53.570    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.availability.ApplicationAvailabilityAutoConfiguration'
2025-08-18 17:34:53.573    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'applicationAvailability'
2025-08-18 17:34:53.585    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'loggingRebinder'
2025-08-18 17:34:53.594    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'braveSpanCustomizer'
2025-08-18 17:34:53.595    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'spanCustomizer'
2025-08-18 17:34:53.595    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'spanCustomizer' via factory method to bean named 'tracing'
2025-08-18 17:34:53.600    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'braveSpanCustomizer' via factory method to bean named 'spanCustomizer'
2025-08-18 17:34:53.605    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'bravePropagator'
2025-08-18 17:34:53.606    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'bravePropagator' via factory method to bean named 'tracing'
2025-08-18 17:34:53.611    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'correlationScopeDecoratorBuilder'
2025-08-18 17:34:53.615    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'spring.sleuth.sampler-org.springframework.cloud.sleuth.autoconfig.brave.SamplerProperties'
2025-08-18 17:34:53.617    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.sleuth.autoconfig.brave.instrument.web.BraveHttpConfiguration$BraveWebFilterConfiguration'
2025-08-18 17:34:53.619    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'braveSpanFromContextRetriever'
2025-08-18 17:34:53.622    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'braveSpanFromContextRetriever' via factory method to bean named 'sleuthCurrentTraceContext'
2025-08-18 17:34:53.622    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'braveSpanFromContextRetriever' via factory method to bean named 'tracer'
2025-08-18 17:34:53.626    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'braveHttpServerHandler'
2025-08-18 17:34:53.626    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'braveHttpServerHandler' via factory method to bean named 'httpTracing'
2025-08-18 17:34:53.640    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'spring.sleuth.http-org.springframework.cloud.sleuth.autoconfig.instrument.web.SleuthHttpProperties'
2025-08-18 17:34:53.642    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'defaultSpanNamer'
2025-08-18 17:34:53.647    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'spring.sleuth.tracer-org.springframework.cloud.sleuth.autoconfig.SleuthTracerProperties'
2025-08-18 17:34:53.651    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'newSpanParser'
2025-08-18 17:34:53.655    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'spelTagValueExpressionResolver'
2025-08-18 17:34:53.660    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'noOpTagValueResolver'
2025-08-18 17:34:53.666    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'reactorSleuthMethodInvocationProcessor'
2025-08-18 17:34:53.682    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.sleuth.autoconfig.brave.instrument.redis.BraveRedisAutoConfiguration'
2025-08-18 17:34:53.685    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.sleuth.autoconfig.instrument.redis.TraceRedisAutoConfiguration'
2025-08-18 17:34:53.689    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration'
2025-08-18 17:34:53.692    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'stringRedisTemplate'
2025-08-18 17:34:53.692    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'stringRedisTemplate' via factory method to bean named 'redisConnectionFactory'
2025-08-18 17:34:53.712    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'java.lang.Object'
2025-08-18 17:34:53.713    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.cache.NoOpCacheConfiguration'
2025-08-18 17:34:53.715    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'cacheManager'
2025-08-18 17:34:53.720    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration'
2025-08-18 17:34:53.723    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'cacheManagerCustomizers'
2025-08-18 17:34:53.730    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'cacheAutoConfigurationValidator'
2025-08-18 17:34:53.731    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'spring.cache-org.springframework.boot.autoconfigure.cache.CacheProperties'
2025-08-18 17:34:53.743    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'cacheAutoConfigurationValidator' via factory method to bean named 'spring.cache-org.springframework.boot.autoconfigure.cache.CacheProperties'
2025-08-18 17:34:53.748    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.context.ConfigurationPropertiesAutoConfiguration'
2025-08-18 17:34:53.752    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.context.LifecycleAutoConfiguration'
2025-08-18 17:34:53.756    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'lifecycleProcessor'
2025-08-18 17:34:53.757    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'spring.lifecycle-org.springframework.boot.autoconfigure.context.LifecycleProperties'
2025-08-18 17:34:53.760    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'lifecycleProcessor' via factory method to bean named 'spring.lifecycle-org.springframework.boot.autoconfigure.context.LifecycleProperties'
2025-08-18 17:34:53.766    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.dao.PersistenceExceptionTranslationAutoConfiguration'
2025-08-18 17:34:53.770    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.sleuth.autoconfig.brave.instrument.mongodb.BraveMongoDbAutoConfiguration'
2025-08-18 17:34:53.775    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'gridFsTemplate'
2025-08-18 17:34:53.777    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'gridFsTemplate' via factory method to bean named 'mongoDatabaseFactory'
2025-08-18 17:34:53.777    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'gridFsTemplate' via factory method to bean named 'mongoTemplate'
2025-08-18 17:34:53.810    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration'
2025-08-18 17:34:53.813    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.data.mongo.MongoRepositoriesAutoConfiguration'
2025-08-18 17:34:53.816    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.data.redis.RedisReactiveAutoConfiguration'
2025-08-18 17:34:53.819    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'reactiveRedisTemplate'
2025-08-18 17:34:53.821    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'reactiveRedisTemplate' via factory method to bean named 'redisConnectionFactory'
2025-08-18 17:34:53.821    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'reactiveRedisTemplate' via factory method to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@4e6f2bb5'
2025-08-18 17:34:54.029    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'reactiveStringRedisTemplate'
2025-08-18 17:34:54.029    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'reactiveStringRedisTemplate' via factory method to bean named 'redisConnectionFactory'
2025-08-18 17:34:54.041    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration'
2025-08-18 17:34:54.045    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'keyValueMappingContext'
2025-08-18 17:34:54.073    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'redisCustomConversions'
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class [B as writing converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.lang.String as reading converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.Number to class [B as writing converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.lang.Number as reading converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.Enum to class [B as writing converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.lang.Enum as reading converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.Boolean to class [B as writing converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.lang.Boolean as reading converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class [B as writing converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.util.Date as reading converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.UUID to class [B as writing converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.util.UUID as reading converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDateTime to class [B as writing converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.LocalDateTime as reading converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDate to class [B as writing converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.LocalDate as reading converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalTime to class [B as writing converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.LocalTime as reading converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.ZonedDateTime to class [B as writing converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.ZonedDateTime as reading converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Instant to class [B as writing converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.Instant as reading converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.ZoneId to class [B as writing converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.ZoneId as reading converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Period to class [B as writing converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.Period as reading converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Duration to class [B as writing converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.Duration as reading converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.joda.time.LocalDate to class java.util.Date as writing converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.joda.time.LocalDateTime to class java.util.Date as writing converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.joda.time.DateTime to class java.util.Date as writing converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class org.joda.time.LocalDate as reading converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class org.joda.time.LocalDateTime as reading converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class org.joda.time.DateTime as reading converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDateTime to class org.joda.time.DateTime as reading converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Instant to class org.joda.time.LocalDateTime as reading converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.joda.time.LocalDateTime to class java.time.Instant as writing converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.joda.time.LocalDateTime to class java.time.LocalDateTime as writing converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class java.time.LocalDateTime as reading converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDateTime to class java.util.Date as writing converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class java.time.LocalDate as reading converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDate to class java.util.Date as writing converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class java.time.LocalTime as reading converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalTime to class java.util.Date as writing converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class java.time.Instant as reading converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Instant to class java.util.Date as writing converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDateTime to class java.time.Instant as reading converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Instant to class java.time.LocalDateTime as reading converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.ZoneId to class java.lang.String as writing converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.ZoneId as reading converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Duration to class java.lang.String as writing converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.Duration as reading converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Period to class java.lang.String as writing converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.Period as reading converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.LocalDate as reading converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.LocalDateTime as reading converter
2025-08-18 17:34:54.074    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.Instant as reading converter
2025-08-18 17:34:54.077    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'redisReferenceResolver'
2025-08-18 17:34:54.081    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'redisConverter'
2025-08-18 17:34:54.082    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class [B as writing converter
2025-08-18 17:34:54.082    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.lang.String as reading converter
2025-08-18 17:34:54.082    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.Number to class [B as writing converter
2025-08-18 17:34:54.082    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.lang.Number as reading converter
2025-08-18 17:34:54.082    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.Enum to class [B as writing converter
2025-08-18 17:34:54.082    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.lang.Enum as reading converter
2025-08-18 17:34:54.082    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.Boolean to class [B as writing converter
2025-08-18 17:34:54.082    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.lang.Boolean as reading converter
2025-08-18 17:34:54.082    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class [B as writing converter
2025-08-18 17:34:54.082    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.util.Date as reading converter
2025-08-18 17:34:54.082    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.UUID to class [B as writing converter
2025-08-18 17:34:54.082    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.util.UUID as reading converter
2025-08-18 17:34:54.082    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDateTime to class [B as writing converter
2025-08-18 17:34:54.082    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.LocalDateTime as reading converter
2025-08-18 17:34:54.082    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDate to class [B as writing converter
2025-08-18 17:34:54.082    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.LocalDate as reading converter
2025-08-18 17:34:54.082    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalTime to class [B as writing converter
2025-08-18 17:34:54.082    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.LocalTime as reading converter
2025-08-18 17:34:54.082    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.ZonedDateTime to class [B as writing converter
2025-08-18 17:34:54.082    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.ZonedDateTime as reading converter
2025-08-18 17:34:54.082    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Instant to class [B as writing converter
2025-08-18 17:34:54.082    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.Instant as reading converter
2025-08-18 17:34:54.082    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.ZoneId to class [B as writing converter
2025-08-18 17:34:54.082    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.ZoneId as reading converter
2025-08-18 17:34:54.082    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Period to class [B as writing converter
2025-08-18 17:34:54.082    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.Period as reading converter
2025-08-18 17:34:54.082    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Duration to class [B as writing converter
2025-08-18 17:34:54.082    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.Duration as reading converter
2025-08-18 17:34:54.082    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.joda.time.LocalDate to class java.util.Date as writing converter
2025-08-18 17:34:54.082    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.joda.time.LocalDateTime to class java.util.Date as writing converter
2025-08-18 17:34:54.082    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.joda.time.DateTime to class java.util.Date as writing converter
2025-08-18 17:34:54.082    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class org.joda.time.LocalDate as reading converter
2025-08-18 17:34:54.082    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class org.joda.time.LocalDateTime as reading converter
2025-08-18 17:34:54.082    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class org.joda.time.DateTime as reading converter
2025-08-18 17:34:54.082    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter
2025-08-18 17:34:54.082    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDateTime to class org.joda.time.DateTime as reading converter
2025-08-18 17:34:54.082    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Instant to class org.joda.time.LocalDateTime as reading converter
2025-08-18 17:34:54.082    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.joda.time.LocalDateTime to class java.time.Instant as writing converter
2025-08-18 17:34:54.082    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.joda.time.LocalDateTime to class java.time.LocalDateTime as writing converter
2025-08-18 17:34:54.082    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class java.time.LocalDateTime as reading converter
2025-08-18 17:34:54.082    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDateTime to class java.util.Date as writing converter
2025-08-18 17:34:54.082    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class java.time.LocalDate as reading converter
2025-08-18 17:34:54.082    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDate to class java.util.Date as writing converter
2025-08-18 17:34:54.082    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class java.time.LocalTime as reading converter
2025-08-18 17:34:54.082    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalTime to class java.util.Date as writing converter
2025-08-18 17:34:54.083    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class java.time.Instant as reading converter
2025-08-18 17:34:54.083    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Instant to class java.util.Date as writing converter
2025-08-18 17:34:54.083    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDateTime to class java.time.Instant as reading converter
2025-08-18 17:34:54.083    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Instant to class java.time.LocalDateTime as reading converter
2025-08-18 17:34:54.083    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.ZoneId to class java.lang.String as writing converter
2025-08-18 17:34:54.083    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.ZoneId as reading converter
2025-08-18 17:34:54.083    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Duration to class java.lang.String as writing converter
2025-08-18 17:34:54.083    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.Duration as reading converter
2025-08-18 17:34:54.083    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Period to class java.lang.String as writing converter
2025-08-18 17:34:54.083    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.Period as reading converter
2025-08-18 17:34:54.083    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.LocalDate as reading converter
2025-08-18 17:34:54.083    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.LocalDateTime as reading converter
2025-08-18 17:34:54.083    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.Instant as reading converter
2025-08-18 17:34:54.083    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class [B as writing converter
2025-08-18 17:34:54.083    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.lang.String as reading converter
2025-08-18 17:34:54.083    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.Number to class [B as writing converter
2025-08-18 17:34:54.083    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.lang.Number as reading converter
2025-08-18 17:34:54.083    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.Enum to class [B as writing converter
2025-08-18 17:34:54.083    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.lang.Enum as reading converter
2025-08-18 17:34:54.083    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.Boolean to class [B as writing converter
2025-08-18 17:34:54.083    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.lang.Boolean as reading converter
2025-08-18 17:34:54.083    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class [B as writing converter
2025-08-18 17:34:54.083    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.util.Date as reading converter
2025-08-18 17:34:54.083    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.UUID to class [B as writing converter
2025-08-18 17:34:54.083    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.util.UUID as reading converter
2025-08-18 17:34:54.083    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDateTime to class [B as writing converter
2025-08-18 17:34:54.083    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.LocalDateTime as reading converter
2025-08-18 17:34:54.083    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDate to class [B as writing converter
2025-08-18 17:34:54.083    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.LocalDate as reading converter
2025-08-18 17:34:54.083    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalTime to class [B as writing converter
2025-08-18 17:34:54.083    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.LocalTime as reading converter
2025-08-18 17:34:54.083    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.ZonedDateTime to class [B as writing converter
2025-08-18 17:34:54.083    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.ZonedDateTime as reading converter
2025-08-18 17:34:54.083    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Instant to class [B as writing converter
2025-08-18 17:34:54.083    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.Instant as reading converter
2025-08-18 17:34:54.083    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.ZoneId to class [B as writing converter
2025-08-18 17:34:54.083    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.ZoneId as reading converter
2025-08-18 17:34:54.084    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Period to class [B as writing converter
2025-08-18 17:34:54.084    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.Period as reading converter
2025-08-18 17:34:54.084    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Duration to class [B as writing converter
2025-08-18 17:34:54.084    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class [B to class java.time.Duration as reading converter
2025-08-18 17:34:54.084    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.joda.time.LocalDate to class java.util.Date as writing converter
2025-08-18 17:34:54.084    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.joda.time.LocalDateTime to class java.util.Date as writing converter
2025-08-18 17:34:54.084    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.joda.time.DateTime to class java.util.Date as writing converter
2025-08-18 17:34:54.084    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class org.joda.time.LocalDate as reading converter
2025-08-18 17:34:54.084    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class org.joda.time.LocalDateTime as reading converter
2025-08-18 17:34:54.084    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class org.joda.time.DateTime as reading converter
2025-08-18 17:34:54.084    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDateTime to class org.joda.time.LocalDateTime as reading converter
2025-08-18 17:34:54.084    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDateTime to class org.joda.time.DateTime as reading converter
2025-08-18 17:34:54.084    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Instant to class org.joda.time.LocalDateTime as reading converter
2025-08-18 17:34:54.084    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.joda.time.LocalDateTime to class java.time.Instant as writing converter
2025-08-18 17:34:54.084    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class org.joda.time.LocalDateTime to class java.time.LocalDateTime as writing converter
2025-08-18 17:34:54.084    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class java.time.LocalDateTime as reading converter
2025-08-18 17:34:54.084    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDateTime to class java.util.Date as writing converter
2025-08-18 17:34:54.084    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class java.time.LocalDate as reading converter
2025-08-18 17:34:54.084    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDate to class java.util.Date as writing converter
2025-08-18 17:34:54.084    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class java.time.LocalTime as reading converter
2025-08-18 17:34:54.084    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalTime to class java.util.Date as writing converter
2025-08-18 17:34:54.084    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.util.Date to class java.time.Instant as reading converter
2025-08-18 17:34:54.084    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Instant to class java.util.Date as writing converter
2025-08-18 17:34:54.084    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.LocalDateTime to class java.time.Instant as reading converter
2025-08-18 17:34:54.084    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Instant to class java.time.LocalDateTime as reading converter
2025-08-18 17:34:54.084    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.ZoneId to class java.lang.String as writing converter
2025-08-18 17:34:54.084    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.ZoneId as reading converter
2025-08-18 17:34:54.084    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Duration to class java.lang.String as writing converter
2025-08-18 17:34:54.084    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.Duration as reading converter
2025-08-18 17:34:54.084    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.time.Period to class java.lang.String as writing converter
2025-08-18 17:34:54.084    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.Period as reading converter
2025-08-18 17:34:54.084    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.LocalDate as reading converter
2025-08-18 17:34:54.084    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.LocalDateTime as reading converter
2025-08-18 17:34:54.084    [main] DEBUG logger_name:o.s.data.convert.CustomConversions - message:Adding converter from class java.lang.String to class java.time.Instant as reading converter
2025-08-18 17:34:54.108    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'redisKeyValueAdapter'
2025-08-18 17:34:54.144    [main] DEBUG logger_name:o.s.d.r.l.RedisMessageListenerContainer - message:Starting RedisMessageListenerContainer...
2025-08-18 17:34:54.146    [main] DEBUG logger_name:o.s.d.r.l.RedisMessageListenerContainer - message:Postpone listening for Redis messages until actual listeners are added.
2025-08-18 17:34:54.170    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'redisKeyValueTemplate'
2025-08-18 17:34:54.187    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration$StringHttpMessageConverterConfiguration'
2025-08-18 17:34:54.189    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'stringHttpMessageConverter'
2025-08-18 17:34:54.190    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'stringHttpMessageConverter' via factory method to bean named 'environment'
2025-08-18 17:34:54.199    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration$MappingJackson2HttpMessageConverterConfiguration'
2025-08-18 17:34:54.201    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'mappingJackson2HttpMessageConverter'
2025-08-18 17:34:54.201    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'mappingJackson2HttpMessageConverter' via factory method to bean named 'jacksonObjectMapper'
2025-08-18 17:34:54.211    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration'
2025-08-18 17:34:54.213    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration'
2025-08-18 17:34:54.217    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'messageConverters'
2025-08-18 17:34:54.245    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.data.web.config.ProjectingArgumentResolverRegistrar'
2025-08-18 17:34:54.246    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration'
2025-08-18 17:34:54.254    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'spring.info-org.springframework.boot.autoconfigure.info.ProjectInfoProperties'
2025-08-18 17:34:54.260    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration' via constructor to bean named 'spring.info-org.springframework.boot.autoconfigure.info.ProjectInfoProperties'
2025-08-18 17:34:54.264    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'scheduledBeanLazyInitializationExcludeFilter'
2025-08-18 17:34:54.273    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.integration.IntegrationAutoConfiguration$IntegrationComponentScanConfiguration'
2025-08-18 17:34:54.276    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.integration.IntegrationAutoConfiguration$IntegrationManagementConfiguration$EnableIntegrationManagementConfiguration'
2025-08-18 17:34:54.280    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.integration.IntegrationAutoConfiguration$IntegrationManagementConfiguration'
2025-08-18 17:34:54.283    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.integration.IntegrationAutoConfiguration$IntegrationConfiguration'
2025-08-18 17:34:54.288    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.integration.context.defaultPollerMetadata'
2025-08-18 17:34:54.289    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'spring.integration-org.springframework.boot.autoconfigure.integration.IntegrationProperties'
2025-08-18 17:34:54.306    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'org.springframework.integration.context.defaultPollerMetadata' via factory method to bean named 'spring.integration-org.springframework.boot.autoconfigure.integration.IntegrationProperties'
2025-08-18 17:34:54.324    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.integration.IntegrationAutoConfiguration'
2025-08-18 17:34:54.327    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'integrationGlobalProperties'
2025-08-18 17:34:54.328    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'integrationGlobalProperties' via factory method to bean named 'spring.integration-org.springframework.boot.autoconfigure.integration.IntegrationProperties'
2025-08-18 17:34:54.336    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.JdbcTemplateConfiguration'
2025-08-18 17:34:54.340    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'jdbcTemplate'
2025-08-18 17:34:54.342    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'spring.jdbc-org.springframework.boot.autoconfigure.jdbc.JdbcProperties'
2025-08-18 17:34:54.347    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'jdbcTemplate' via factory method to bean named 'dataSource'
2025-08-18 17:34:54.347    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'jdbcTemplate' via factory method to bean named 'spring.jdbc-org.springframework.boot.autoconfigure.jdbc.JdbcProperties'
2025-08-18 17:34:54.409    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.NamedParameterJdbcTemplateConfiguration'
2025-08-18 17:34:54.412    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'namedParameterJdbcTemplate'
2025-08-18 17:34:54.413    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'namedParameterJdbcTemplate' via factory method to bean named 'jdbcTemplate'
2025-08-18 17:34:54.440    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration'
2025-08-18 17:34:54.444    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.netty.NettyAutoConfiguration'
2025-08-18 17:34:54.446    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'spring.netty-org.springframework.boot.autoconfigure.netty.NettyProperties'
2025-08-18 17:34:54.449    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'org.springframework.boot.autoconfigure.netty.NettyAutoConfiguration' via constructor to bean named 'spring.netty-org.springframework.boot.autoconfigure.netty.NettyProperties'
2025-08-18 17:34:54.452    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.sql.init.SqlInitializationAutoConfiguration'
2025-08-18 17:34:54.456    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration$JdbcTransactionManagerConfiguration'
2025-08-18 17:34:54.460    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'transactionManager'
2025-08-18 17:34:54.460    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'transactionManager' via factory method to bean named 'environment'
2025-08-18 17:34:54.460    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'transactionManager' via factory method to bean named 'dataSource'
2025-08-18 17:34:54.509    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'platformTransactionManagerCustomizers'
2025-08-18 17:34:54.509    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration'
2025-08-18 17:34:54.517    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'spring.transaction-org.springframework.boot.autoconfigure.transaction.TransactionProperties'
2025-08-18 17:34:54.545    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration'
2025-08-18 17:34:54.549    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration$CglibAutoProxyConfiguration'
2025-08-18 17:34:54.553    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration'
2025-08-18 17:34:54.557    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$TransactionTemplateConfiguration'
2025-08-18 17:34:54.560    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'transactionTemplate'
2025-08-18 17:34:54.561    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'transactionTemplate' via factory method to bean named 'transactionManager'
2025-08-18 17:34:54.582    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.client.RestTemplateAutoConfiguration'
2025-08-18 17:34:54.585    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.embedded.EmbeddedWebServerFactoryCustomizerAutoConfiguration'
2025-08-18 17:34:54.589    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration'
2025-08-18 17:34:54.590    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'spring.servlet.multipart-org.springframework.boot.autoconfigure.web.servlet.MultipartProperties'
2025-08-18 17:34:54.602    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration' via constructor to bean named 'spring.servlet.multipart-org.springframework.boot.autoconfigure.web.servlet.MultipartProperties'
2025-08-18 17:34:54.606    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'multipartResolver'
2025-08-18 17:34:54.611    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration'
2025-08-18 17:34:54.614    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'configurationPropertiesRebinder'
2025-08-18 17:34:54.615    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'configurationPropertiesRebinder' via factory method to bean named 'configurationPropertiesBeans'
2025-08-18 17:34:54.624    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.autoconfigure.LifecycleMvcEndpointAutoConfiguration'
2025-08-18 17:34:54.626    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'environmentManager'
2025-08-18 17:34:54.627    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'environmentManager' via factory method to bean named 'environment'
2025-08-18 17:34:54.633    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.client.discovery.composite.CompositeDiscoveryClientAutoConfiguration'
2025-08-18 17:34:54.635    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'compositeDiscoveryClient'
2025-08-18 17:34:54.636    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'simpleDiscoveryClient'
2025-08-18 17:34:54.636    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.client.discovery.simple.SimpleDiscoveryClientAutoConfiguration'
2025-08-18 17:34:54.657    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'simpleDiscoveryProperties'
2025-08-18 17:34:55.553    [cluster-ClusterId{value='68a2f3ab92e95743146149d3', description='null'}-localhost:27017] DEBUG logger_name:org.mongodb.driver.cluster - message:Updating cluster description to  {type=STANDALONE, servers=[{address=localhost:27017, type=STANDALONE, roundTripTime=102.3 ms, state=CONNECTED}]
2025-08-18 17:34:55.553    [cluster-ClusterId{value='68a2f3ab92e95743146149d3', description='null'}-localhost:27017] DEBUG logger_name:org.mongodb.driver.cluster - message:Checking status of localhost:27017
2025-08-18 17:34:55.585    [cluster-rtt-ClusterId{value='68a2f3ab92e95743146149d3', description='null'}-localhost:27017] DEBUG logger_name:org.mongodb.driver.protocol.command - message:Sending command '{"hello": 1, "$db": "admin", "$readPreference": {"mode": "primaryPreferred"}}' with request id 5 to database admin on connection [connectionId{localValue:1, serverValue:22}] to server localhost:27017
2025-08-18 17:34:55.586    [cluster-rtt-ClusterId{value='68a2f3ab92e95743146149d3', description='null'}-localhost:27017] DEBUG logger_name:org.mongodb.driver.protocol.command - message:Execution of command with request id 5 completed successfully in 1.48 ms on connection [connectionId{localValue:1, serverValue:22}] to server localhost:27017
2025-08-18 17:34:56.025    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'simpleDiscoveryClient' via factory method to bean named 'simpleDiscoveryProperties'
2025-08-18 17:34:56.030    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'compositeDiscoveryClient' via factory method to bean named 'nacosDiscoveryClient'
2025-08-18 17:34:56.030    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'compositeDiscoveryClient' via factory method to bean named 'simpleDiscoveryClient'
2025-08-18 17:34:56.035    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.client.CommonsClientAutoConfiguration'
2025-08-18 17:34:56.038    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.client.ReactiveCommonsClientAutoConfiguration'
2025-08-18 17:34:56.041    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'zoneConfig'
2025-08-18 17:34:56.042    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'zoneConfig' via factory method to bean named 'environment'
2025-08-18 17:34:56.045    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'loadBalancerServiceInstanceCookieTransformer'
2025-08-18 17:34:56.045    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'loadBalancerServiceInstanceCookieTransformer' via factory method to bean named 'loadBalancerClientFactory'
2025-08-18 17:34:56.049    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'xForwarderHeadersTransformer'
2025-08-18 17:34:56.049    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'xForwarderHeadersTransformer' via factory method to bean named 'loadBalancerClientFactory'
2025-08-18 17:34:56.052    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.client.loadbalancer.AsyncLoadBalancerAutoConfiguration$LoadBalancerInterceptorConfig'
2025-08-18 17:34:56.055    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'asyncLoadBalancerInterceptor'
2025-08-18 17:34:56.056    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'asyncLoadBalancerInterceptor' via factory method to bean named 'blockingLoadBalancerClient'
2025-08-18 17:34:56.061    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'asyncRestTemplateCustomizer'
2025-08-18 17:34:56.062    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'asyncRestTemplateCustomizer' via factory method to bean named 'asyncLoadBalancerInterceptor'
2025-08-18 17:34:56.079    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.client.loadbalancer.AsyncLoadBalancerAutoConfiguration$AsyncRestTemplateCustomizerConfig'
2025-08-18 17:34:56.083    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'loadBalancedAsyncRestTemplateInitializer'
2025-08-18 17:34:56.085    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'loadBalancedAsyncRestTemplateInitializer' via factory method to bean named 'asyncRestTemplateCustomizer'
2025-08-18 17:34:56.100    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.client.loadbalancer.AsyncLoadBalancerAutoConfiguration'
2025-08-18 17:34:56.104    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'loadBalancerInterceptor'
2025-08-18 17:34:56.105    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'loadBalancerRequestFactory'
2025-08-18 17:34:56.105    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration'
2025-08-18 17:34:56.110    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'loadBalancerRequestFactory' via factory method to bean named 'blockingLoadBalancerClient'
2025-08-18 17:34:56.115    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'loadBalancerInterceptor' via factory method to bean named 'blockingLoadBalancerClient'
2025-08-18 17:34:56.115    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'loadBalancerInterceptor' via factory method to bean named 'loadBalancerRequestFactory'
2025-08-18 17:34:56.115    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'loadBalancerInterceptor' via factory method to bean named 'loadBalancedRetryFactory'
2025-08-18 17:34:56.115    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'loadBalancerInterceptor' via factory method to bean named 'loadBalancerClientFactory'
2025-08-18 17:34:56.120    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'restTemplateCustomizer'
2025-08-18 17:34:56.122    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'restTemplateCustomizer' via factory method to bean named 'loadBalancerInterceptor'
2025-08-18 17:34:56.137    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$RetryAutoConfiguration'
2025-08-18 17:34:56.142    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'loadBalancedRestTemplateInitializerDeprecated'
2025-08-18 17:34:56.156    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.client.serviceregistry.ServiceRegistryAutoConfiguration'
2025-08-18 17:34:56.159    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.sleuth.autoconfig.brave.instrument.web.client.BraveWebClientAutoConfiguration$HttpAsyncClientBuilderConfig'
2025-08-18 17:34:56.161    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'traceHttpAsyncClientBuilder'
2025-08-18 17:34:56.161    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'traceHttpAsyncClientBuilder' via factory method to bean named 'httpTracing'
2025-08-18 17:34:56.187    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.sleuth.autoconfig.brave.instrument.web.client.BraveWebClientAutoConfiguration$HttpClientBuilderConfig'
2025-08-18 17:34:56.189    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'traceHttpClientBuilder'
2025-08-18 17:34:56.189    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'traceHttpClientBuilder' via factory method to bean named 'httpTracing'
2025-08-18 17:34:56.203    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.sleuth.autoconfig.brave.instrument.web.client.BraveWebClientAutoConfiguration'
2025-08-18 17:34:56.206    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'traceRestTemplateCustomizer'
2025-08-18 17:34:56.206    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'traceRestTemplateCustomizer' via factory method to bean named 'org.springframework.beans.factory.support.DefaultListableBeanFactory@5e230fc6'
2025-08-18 17:34:56.210    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.sleuth.autoconfig.instrument.web.client.TraceWebClientAutoConfiguration'
2025-08-18 17:34:56.213    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.commons.httpclient.HttpClientConfiguration$ApacheHttpClientConfiguration'
2025-08-18 17:34:56.216    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'connManFactory'
2025-08-18 17:34:56.222    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'apacheHttpClientFactory'
2025-08-18 17:34:56.223    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'apacheHttpClientFactory' via factory method to bean named 'traceHttpClientBuilder'
2025-08-18 17:34:56.228    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.commons.httpclient.HttpClientConfiguration'
2025-08-18 17:34:56.232    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.configuration.CompatibilityVerifierAutoConfiguration'
2025-08-18 17:34:56.235    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'compositeCompatibilityVerifier'
2025-08-18 17:34:56.236    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'springBootVersionVerifier'
2025-08-18 17:34:56.237    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'spring.cloud.compatibility-verifier-org.springframework.cloud.configuration.CompatibilityVerifierProperties'
2025-08-18 17:34:56.241    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'springBootVersionVerifier' via factory method to bean named 'spring.cloud.compatibility-verifier-org.springframework.cloud.configuration.CompatibilityVerifierProperties'
2025-08-18 17:34:56.257    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'compositeCompatibilityVerifier' via factory method to bean named 'springBootVersionVerifier'
2025-08-18 17:34:56.262    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.function.cloudevent.CloudEventsFunctionExtensionConfiguration'
2025-08-18 17:34:56.265    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'nativeFunctionInvocationHelper'
2025-08-18 17:34:56.276    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.function.context.config.ContextFunctionCatalogAutoConfiguration$JsonMapperConfiguration'
2025-08-18 17:34:56.279    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'jsonMapper'
2025-08-18 17:34:56.279    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'jsonMapper' via factory method to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@4e6f2bb5'
2025-08-18 17:34:56.287    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.function.context.config.ContextFunctionCatalogAutoConfiguration$PlainFunctionScanConfiguration'
2025-08-18 17:34:56.291    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.function.context.config.ContextFunctionCatalogAutoConfiguration'
2025-08-18 17:34:56.294    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'functionCatalog'
2025-08-18 17:34:56.296    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'integrationArgumentResolverMessageConverter'
2025-08-18 17:34:56.296    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.stream.config.ContentTypeConfiguration'
2025-08-18 17:34:56.298    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'datatypeChannelMessageConverter'
2025-08-18 17:34:56.301    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'integrationArgumentResolverMessageConverter' via factory method to bean named 'datatypeChannelMessageConverter'
2025-08-18 17:34:56.357    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'functionCatalog' via factory method to bean named 'integrationArgumentResolverMessageConverter'
2025-08-18 17:34:56.357    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'functionCatalog' via factory method to bean named 'datatypeChannelMessageConverter'
2025-08-18 17:34:56.357    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'functionCatalog' via factory method to bean named 'jsonMapper'
2025-08-18 17:34:56.357    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'functionCatalog' via factory method to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@4e6f2bb5'
2025-08-18 17:34:56.357    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'functionCatalog' via factory method to bean named 'nativeFunctionInvocationHelper'
2025-08-18 17:34:56.358    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'jsonNodeWrapperToJsonNodeConverter'
2025-08-18 17:34:56.380    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'spring.cloud.function-org.springframework.cloud.function.context.FunctionProperties'
2025-08-18 17:34:56.403    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'traceFunctionAroundWrapper'
2025-08-18 17:34:56.403    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.sleuth.autoconfig.instrument.messaging.TraceFunctionAutoConfiguration'
2025-08-18 17:34:56.408    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'traceMessagePropagationSetter'
2025-08-18 17:34:56.408    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.sleuth.autoconfig.instrument.messaging.TraceSpringMessagingAutoConfiguration'
2025-08-18 17:34:56.420    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'traceMessagePropagationGetter'
2025-08-18 17:34:56.427    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'traceFunctionAroundWrapper' via factory method to bean named 'environment'
2025-08-18 17:34:56.427    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'traceFunctionAroundWrapper' via factory method to bean named 'braveTracer'
2025-08-18 17:34:56.427    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'traceFunctionAroundWrapper' via factory method to bean named 'bravePropagator'
2025-08-18 17:34:56.427    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'traceFunctionAroundWrapper' via factory method to bean named 'traceMessagePropagationSetter'
2025-08-18 17:34:56.427    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'traceFunctionAroundWrapper' via factory method to bean named 'traceMessagePropagationGetter'
2025-08-18 17:34:56.436    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'traceRabbitFunctionMessageSpanCustomizer'
2025-08-18 17:34:56.436    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.sleuth.autoconfig.instrument.messaging.TraceFunctionAutoConfiguration$TraceFunctionStreamConfiguration$RabbitOnlyStreamConfiguration'
2025-08-18 17:34:56.495    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'functionRouter'
2025-08-18 17:34:56.497    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'functionRouter' via factory method to bean named 'functionCatalog'
2025-08-18 17:34:56.497    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'functionRouter' via factory method to bean named 'spring.cloud.function-org.springframework.cloud.function.context.FunctionProperties'
2025-08-18 17:34:56.497    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'functionRouter' via factory method to bean named 'org.springframework.beans.factory.support.DefaultListableBeanFactory@5e230fc6'
2025-08-18 17:34:56.518    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.function.context.config.KotlinLambdaToFunctionAutoConfiguration'
2025-08-18 17:34:56.526    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$DefaultLoadBalancerCacheManagerConfiguration'
2025-08-18 17:34:56.528    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'defaultLoadBalancerCacheManager'
2025-08-18 17:34:56.530    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'spring.cloud.loadbalancer.cache-org.springframework.cloud.loadbalancer.cache.LoadBalancerCacheProperties'
2025-08-18 17:34:56.536    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'defaultLoadBalancerCacheManager' via factory method to bean named 'spring.cloud.loadbalancer.cache-org.springframework.cloud.loadbalancer.cache.LoadBalancerCacheProperties'
2025-08-18 17:34:56.584    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCacheManagerWarnConfiguration'
2025-08-18 17:34:56.586    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'caffeineWarnLogger'
2025-08-18 17:34:56.590    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration'
2025-08-18 17:34:56.593    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.sleuth.autoconfig.instrument.web.client.feign.TraceFeignClientAutoConfiguration$OkHttpClientFeignBeanPostProcessorConfiguration'
2025-08-18 17:34:56.595    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.sleuth.autoconfig.instrument.web.client.feign.TraceFeignClientAutoConfiguration$FeignBeanPostProcessorConfiguration'
2025-08-18 17:34:56.597    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.sleuth.autoconfig.instrument.web.client.feign.TraceFeignClientAutoConfiguration'
2025-08-18 17:34:56.600    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'traceFeignAspect'
2025-08-18 17:34:56.600    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'traceFeignAspect' via factory method to bean named 'org.springframework.beans.factory.support.DefaultListableBeanFactory@5e230fc6'
2025-08-18 17:34:56.600    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.openfeign.loadbalancer.DefaultFeignLoadBalancerConfiguration'
2025-08-18 17:34:56.604    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.openfeign.loadbalancer.FeignLoadBalancerAutoConfiguration'
2025-08-18 17:34:56.607    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'feignFeature'
2025-08-18 17:34:56.670    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.sleuth.autoconfig.brave.instrument.rpc.BraveRpcAutoConfiguration'
2025-08-18 17:34:56.674    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'rpcTracing'
2025-08-18 17:34:56.683    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'rpcTracing' via factory method to bean named 'tracing'
2025-08-18 17:34:56.706    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.sleuth.autoconfig.instrument.async.TraceAsyncAutoConfiguration'
2025-08-18 17:34:56.709    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.sleuth.autoconfig.instrument.scheduling.TraceSchedulingAutoConfiguration'
2025-08-18 17:34:56.712    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'traceSchedulingAspect'
2025-08-18 17:34:56.713    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'spring.sleuth.scheduled-org.springframework.cloud.sleuth.autoconfig.instrument.scheduling.SleuthSchedulingProperties'
2025-08-18 17:34:56.716    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'traceSchedulingAspect' via factory method to bean named 'braveTracer'
2025-08-18 17:34:56.716    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'traceSchedulingAspect' via factory method to bean named 'spring.sleuth.scheduled-org.springframework.cloud.sleuth.autoconfig.instrument.scheduling.SleuthSchedulingProperties'
2025-08-18 17:34:56.716    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.sleuth.autoconfig.instrument.async.TraceAsyncDefaultAutoConfiguration$DefaultAsyncConfigurerSupport'
2025-08-18 17:34:56.724    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.sleuth.autoconfig.instrument.async.TraceAsyncDefaultAutoConfiguration'
2025-08-18 17:34:56.726    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'traceAsyncAspect'
2025-08-18 17:34:56.727    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'traceAsyncAspect' via factory method to bean named 'braveTracer'
2025-08-18 17:34:56.727    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'traceAsyncAspect' via factory method to bean named 'defaultSpanNamer'
2025-08-18 17:34:56.728    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.sleuth.autoconfig.instrument.circuitbreaker.TraceCircuitBreakerAutoConfiguration'
2025-08-18 17:34:56.730    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'traceCircuitBreakerFactoryAspect'
2025-08-18 17:34:56.730    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'traceCircuitBreakerFactoryAspect' via factory method to bean named 'braveTracer'
2025-08-18 17:34:56.730    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'traceReactiveCircuitBreakerFactoryAspect'
2025-08-18 17:34:56.730    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'traceReactiveCircuitBreakerFactoryAspect' via factory method to bean named 'braveTracer'
2025-08-18 17:34:56.730    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'traceReactiveCircuitBreakerFactoryAspect' via factory method to bean named 'braveCurrentTraceContext'
2025-08-18 17:34:56.731    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'spring.sleuth.circuitbreaker-org.springframework.cloud.sleuth.autoconfig.instrument.circuitbreaker.SleuthCircuitBreakerProperties'
2025-08-18 17:34:56.733    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.sleuth.autoconfig.instrument.jdbc.TraceJdbcAutoConfiguration'
2025-08-18 17:34:56.736    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'traceDataSourceNameResolver'
2025-08-18 17:34:56.741    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'traceHikariListenerStrategySpanCustomizer'
2025-08-18 17:34:56.745    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'spring.sleuth.jdbc-org.springframework.cloud.sleuth.autoconfig.instrument.jdbc.TraceJdbcProperties'
2025-08-18 17:34:56.770    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.sleuth.autoconfig.instrument.messaging.TraceFunctionAutoConfiguration$TraceFunctionStreamConfiguration'
2025-08-18 17:34:56.773    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'traceMessagingAspect'
2025-08-18 17:34:56.774    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'traceMessagingAspect' via factory method to bean named 'braveTracer'
2025-08-18 17:34:56.774    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'traceMessagingAspect' via factory method to bean named 'defaultSpanNamer'
2025-08-18 17:34:56.774    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'spring.sleuth.integration-org.springframework.cloud.sleuth.autoconfig.instrument.messaging.SleuthIntegrationMessagingProperties'
2025-08-18 17:34:56.776    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.sleuth.autoconfig.instrument.prometheus.PrometheusExemplarsAutoConfiguration'
2025-08-18 17:34:56.778    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'sleuthSpanContextSupplier'
2025-08-18 17:34:56.783    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.sleuth.autoconfig.instrument.quartz.TraceQuartzAutoConfiguration$Config'
2025-08-18 17:34:56.784    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'tracingJobListener'
2025-08-18 17:34:56.784    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'tracingJobListener' via factory method to bean named 'braveTracer'
2025-08-18 17:34:56.784    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'tracingJobListener' via factory method to bean named 'bravePropagator'
2025-08-18 17:34:56.794    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.sleuth.autoconfig.instrument.quartz.TraceQuartzAutoConfiguration'
2025-08-18 17:34:56.807    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'org.springframework.cloud.sleuth.autoconfig.instrument.quartz.TraceQuartzAutoConfiguration' via constructor to bean named 'quartzScheduler'
2025-08-18 17:34:56.807    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'org.springframework.cloud.sleuth.autoconfig.instrument.quartz.TraceQuartzAutoConfiguration' via constructor to bean named 'org.springframework.beans.factory.support.DefaultListableBeanFactory@5e230fc6'
2025-08-18 17:34:56.812    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$TraceWebMvcAutoConfiguration'
2025-08-18 17:34:56.815    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'traceWebAspect'
2025-08-18 17:34:56.815    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'traceWebAspect' via factory method to bean named 'braveTracer'
2025-08-18 17:34:56.815    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'traceWebAspect' via factory method to bean named 'braveCurrentTraceContext'
2025-08-18 17:34:56.815    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'traceWebAspect' via factory method to bean named 'defaultSpanNamer'
2025-08-18 17:34:56.816    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebAutoConfiguration'
2025-08-18 17:34:56.819    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration$TraceReactorConfiguration$HooksRefresherConfiguration'
2025-08-18 17:34:56.821    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'hooksRefresher'
2025-08-18 17:34:56.822    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'spring.sleuth.reactor-org.springframework.cloud.sleuth.autoconfig.instrument.reactor.SleuthReactorProperties'
2025-08-18 17:34:56.825    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'hooksRefresher' via factory method to bean named 'spring.sleuth.reactor-org.springframework.cloud.sleuth.autoconfig.instrument.reactor.SleuthReactorProperties'
2025-08-18 17:34:56.825    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'hooksRefresher' via factory method to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@4e6f2bb5'
2025-08-18 17:34:56.829    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.sleuth.autoconfig.instrument.reactor.TraceReactorAutoConfiguration'
2025-08-18 17:34:56.832    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.sleuth.autoconfig.instrument.tx.TraceTxAutoConfiguration'
2025-08-18 17:34:56.834    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.sleuth.autoconfig.instrument.web.client.TraceWebAsyncClientAutoConfiguration'
2025-08-18 17:34:56.837    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'integrationMessageHandlerMethodFactory'
2025-08-18 17:34:56.840    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'integrationMessageHandlerMethodFactory' via factory method to bean named 'integrationArgumentResolverMessageConverter'
2025-08-18 17:34:56.840    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'integrationMessageHandlerMethodFactory' via factory method to bean named 'defaultValidator'
2025-08-18 17:34:56.840    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'integrationMessageHandlerMethodFactory' via factory method to bean named 'org.springframework.beans.factory.support.DefaultListableBeanFactory@5e230fc6'
2025-08-18 17:34:56.885    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'messageConverterConfigurer'
2025-08-18 17:34:56.887    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'spring.cloud.stream.function-org.springframework.cloud.stream.function.StreamFunctionProperties'
2025-08-18 17:34:56.892    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'messageConverterConfigurer' via factory method to bean named 'spring.cloud.stream-org.springframework.cloud.stream.config.BindingServiceProperties'
2025-08-18 17:34:56.892    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'messageConverterConfigurer' via factory method to bean named 'integrationArgumentResolverMessageConverter'
2025-08-18 17:34:56.892    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'messageConverterConfigurer' via factory method to bean named 'spring.cloud.stream.function-org.springframework.cloud.stream.function.StreamFunctionProperties'
2025-08-18 17:34:56.902    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'channelFactory'
2025-08-18 17:34:56.903    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'compositeMessageChannelConfigurer'
2025-08-18 17:34:56.905    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'compositeMessageChannelConfigurer' via factory method to bean named 'messageConverterConfigurer'
2025-08-18 17:34:56.909    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'channelFactory' via factory method to bean named 'compositeMessageChannelConfigurer'
2025-08-18 17:34:56.917    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'messageSourceFactory'
2025-08-18 17:34:56.918    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'messageSourceFactory' via factory method to bean named 'integrationArgumentResolverMessageConverter'
2025-08-18 17:34:56.918    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'messageSourceFactory' via factory method to bean named 'compositeMessageChannelConfigurer'
2025-08-18 17:34:56.923    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'streamBridgeUtils'
2025-08-18 17:34:56.924    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'streamBridgeUtils' via factory method to bean named 'functionCatalog'
2025-08-18 17:34:56.924    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'streamBridgeUtils' via factory method to bean named 'spring.cloud.stream-org.springframework.cloud.stream.config.BindingServiceProperties'
2025-08-18 17:34:56.924    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'streamBridgeUtils' via factory method to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@4e6f2bb5'
2025-08-18 17:34:56.933    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'functionBindingRegistrar'
2025-08-18 17:34:56.934    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'functionBindingRegistrar' via factory method to bean named 'environment'
2025-08-18 17:34:56.934    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'functionBindingRegistrar' via factory method to bean named 'functionCatalog'
2025-08-18 17:34:56.934    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'functionBindingRegistrar' via factory method to bean named 'spring.cloud.stream.function-org.springframework.cloud.stream.function.StreamFunctionProperties'
2025-08-18 17:34:57.016    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'inMemorySwaggerResourcesProvider_binding'
2025-08-18 17:34:57.064    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'functionInitializer'
2025-08-18 17:34:57.066    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'functionInitializer' via factory method to bean named 'functionCatalog'
2025-08-18 17:34:57.066    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'functionInitializer' via factory method to bean named 'spring.cloud.stream.function-org.springframework.cloud.stream.function.StreamFunctionProperties'
2025-08-18 17:34:57.066    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'functionInitializer' via factory method to bean named 'spring.cloud.stream-org.springframework.cloud.stream.config.BindingServiceProperties'
2025-08-18 17:34:57.066    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'functionInitializer' via factory method to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@4e6f2bb5'
2025-08-18 17:34:57.066    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'functionInitializer' via factory method to bean named 'streamBridgeUtils'
2025-08-18 17:34:57.085    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'supplierInitializer'
2025-08-18 17:34:57.089    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'supplierInitializer' via factory method to bean named 'functionCatalog'
2025-08-18 17:34:57.089    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'supplierInitializer' via factory method to bean named 'spring.cloud.stream.function-org.springframework.cloud.stream.function.StreamFunctionProperties'
2025-08-18 17:34:57.089    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'supplierInitializer' via factory method to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@4e6f2bb5'
2025-08-18 17:34:57.089    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'supplierInitializer' via factory method to bean named 'spring.cloud.stream-org.springframework.cloud.stream.config.BindingServiceProperties'
2025-08-18 17:34:57.089    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'supplierInitializer' via factory method to bean named '&inMemorySwaggerResourcesProvider_binding'
2025-08-18 17:34:57.089    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'supplierInitializer' via factory method to bean named 'streamBridgeUtils'
2025-08-18 17:34:57.089    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'supplierInitializer' via factory method to bean named 'taskScheduler'
2025-08-18 17:34:57.275    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.integration.dsl.context.IntegrationFlowContext'
2025-08-18 17:34:57.282    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'inMemorySwaggerResourcesProvider-out-0_spca'
2025-08-18 17:34:57.307    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'messageBuilderFactory'
2025-08-18 17:34:57.336    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'mergedIntegrationGlobalProperties'
2025-08-18 17:34:57.359    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'integrationMessagePublishingErrorHandler'
2025-08-18 17:34:57.367    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'integrationChannelResolver'
2025-08-18 17:34:57.422    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'inMemorySwaggerResourcesProvider-out-0_spca.source'
2025-08-18 17:34:57.428    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'integrationEvaluationContext'
2025-08-18 17:34:57.429    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'jsonPath'
2025-08-18 17:34:57.436    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'spelPropertyAccessorRegistrar'
2025-08-18 17:34:57.481    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'inMemorySwaggerResourcesProvider_integrationflow.channel#0'
2025-08-18 17:34:57.518    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'inMemorySwaggerResourcesProvider_integrationflow.router#0'
2025-08-18 17:34:57.572    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'inMemorySwaggerResourcesProvider_integrationflow.org.springframework.integration.config.ConsumerEndpointFactoryBean#0'
2025-08-18 17:34:57.606    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'messageChannelStreamListenerResultAdapter'
2025-08-18 17:34:57.610    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'outputBindingLifecycle'
2025-08-18 17:34:57.612    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'dynamicDestinationsBindable'
2025-08-18 17:34:57.617    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'outputBindingLifecycle' via factory method to bean named 'bindingService'
2025-08-18 17:34:57.617    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'outputBindingLifecycle' via factory method to bean named 'dynamicDestinationsBindable'
2025-08-18 17:34:57.617    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'outputBindingLifecycle' via factory method to bean named '&inMemorySwaggerResourcesProvider_binding'
2025-08-18 17:34:57.626    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'inputBindingLifecycle'
2025-08-18 17:34:57.626    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'inputBindingLifecycle' via factory method to bean named 'bindingService'
2025-08-18 17:34:57.626    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'inputBindingLifecycle' via factory method to bean named 'dynamicDestinationsBindable'
2025-08-18 17:34:57.626    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'inputBindingLifecycle' via factory method to bean named '&inMemorySwaggerResourcesProvider_binding'
2025-08-18 17:34:57.633    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'bindingsLifecycleController'
2025-08-18 17:34:57.635    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'bindingsLifecycleController' via factory method to bean named 'inputBindingLifecycle'
2025-08-18 17:34:57.635    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'bindingsLifecycleController' via factory method to bean named 'outputBindingLifecycle'
2025-08-18 17:34:57.643    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'binderAwareChannelResolver'
2025-08-18 17:34:57.646    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'binderAwareChannelResolver' via factory method to bean named 'bindingService'
2025-08-18 17:34:57.646    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'binderAwareChannelResolver' via factory method to bean named 'channelFactory'
2025-08-18 17:34:57.646    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'binderAwareChannelResolver' via factory method to bean named 'dynamicDestinationsBindable'
2025-08-18 17:34:57.651    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'binderAwareRouterBeanPostProcessor'
2025-08-18 17:34:57.655    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'binderAwareRouterBeanPostProcessor' via factory method to bean named 'inMemorySwaggerResourcesProvider_integrationflow.router#0'
2025-08-18 17:34:57.655    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'binderAwareRouterBeanPostProcessor' via factory method to bean named 'binderAwareChannelResolver'
2025-08-18 17:34:57.657    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'springfox.documentation.oas.configuration.OpenApiWebMvcConfiguration'
2025-08-18 17:34:57.661    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'webMvcOpenApiTransformer'
2025-08-18 17:34:57.673    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'springfox.documentation.oas.configuration.OpenApiDocumentationConfiguration'
2025-08-18 17:34:57.675    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'springfox.bean.validators.configuration.BeanValidatorPluginsConfiguration'
2025-08-18 17:34:57.682    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'expanderMinMax'
2025-08-18 17:34:57.693    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'expanderNotNull'
2025-08-18 17:34:57.697    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'expanderNotBlank'
2025-08-18 17:34:57.702    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'expanderPattern'
2025-08-18 17:34:57.709    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'expanderSize'
2025-08-18 17:34:57.714    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'parameterMinMax'
2025-08-18 17:34:57.718    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'parameterNotNull'
2025-08-18 17:34:57.724    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'parameterNotBlank'
2025-08-18 17:34:57.728    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'parameterPattern'
2025-08-18 17:34:57.733    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'parameterSize'
2025-08-18 17:34:57.738    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'minMaxPlugin'
2025-08-18 17:34:57.743    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'decimalMinMaxPlugin'
2025-08-18 17:34:57.748    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'sizePlugin'
2025-08-18 17:34:57.753    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'isNullPlugin'
2025-08-18 17:34:57.758    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'notNullPlugin'
2025-08-18 17:34:57.762    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'notBlankPlugin'
2025-08-18 17:34:57.767    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'patternPlugin'
2025-08-18 17:34:57.776    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'springfox.boot.starter.autoconfigure.OpenApiAutoConfiguration'
2025-08-18 17:34:57.780    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'springfox.documentation-springfox.boot.starter.autoconfigure.SpringfoxConfigurationProperties'
2025-08-18 17:34:57.784    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'sysFileMapper'
2025-08-18 17:34:57.846    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'nullChannel'
2025-08-18 17:34:57.861    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'errorChannel'
2025-08-18 17:34:57.948    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean '_org.springframework.integration.errorLogger.handler'
2025-08-18 17:34:58.011    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean '_org.springframework.integration.errorLogger'
2025-08-18 17:34:58.036    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'integrationSimpleEvaluationContext'
2025-08-18 17:34:58.042    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'integrationLifecycleRoleController'
2025-08-18 17:34:58.047    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'integrationHeaderChannelRegistry'
2025-08-18 17:34:58.060    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'integrationListMessageHandlerMethodFactory'
2025-08-18 17:34:58.076    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'converterRegistrar'
2025-08-18 17:34:58.143    [main] DEBUG logger_name:o.s.c.e.EventListenerMethodProcessor - message:1 @EventListener methods processed on bean 'nacosAutoServiceRegistration': {public void com.alibaba.cloud.nacos.registry.NacosAutoServiceRegistration.onNacosDiscoveryInfoChangedEvent(com.alibaba.cloud.nacos.event.NacosDiscoveryInfoChangedEvent)=@org.springframework.context.event.EventListener(classes={}, condition="", id="", value={})}
2025-08-18 17:34:58.178    [main] DEBUG logger_name:o.s.j.e.a.AnnotationMBeanExporter - message:Registering beans for JMX exposure on startup
2025-08-18 17:34:58.178    [main] DEBUG logger_name:o.s.j.e.a.AnnotationMBeanExporter - message:Autodetecting user-defined JMX MBeans
2025-08-18 17:34:58.182    [main] DEBUG logger_name:o.s.j.e.a.AnnotationMBeanExporter - message:Bean with name 'dataSource' has been autodetected for JMX exposure
2025-08-18 17:34:58.201    [main] DEBUG logger_name:o.s.j.e.a.AnnotationMBeanExporter - message:Bean with name 'rabbitAdmin' has been autodetected for JMX exposure
2025-08-18 17:34:58.202    [main] DEBUG logger_name:o.s.j.e.a.AnnotationMBeanExporter - message:Bean with name 'rabbitConnectionFactory' has been autodetected for JMX exposure
2025-08-18 17:34:58.203    [main] DEBUG logger_name:o.s.j.e.a.AnnotationMBeanExporter - message:Bean with name 'refreshScope' has been autodetected for JMX exposure
2025-08-18 17:34:58.204    [main] DEBUG logger_name:o.s.j.e.a.AnnotationMBeanExporter - message:Bean with name 'integrationMbeanExporter' has been autodetected for JMX exposure
2025-08-18 17:34:58.205    [main] DEBUG logger_name:o.s.j.e.a.AnnotationMBeanExporter - message:Bean with name 'configurationPropertiesRebinder' has been autodetected for JMX exposure
2025-08-18 17:34:58.205    [main] DEBUG logger_name:o.s.j.e.a.AnnotationMBeanExporter - message:Bean with name 'environmentManager' has been autodetected for JMX exposure
2025-08-18 17:34:58.214    [main] DEBUG logger_name:o.s.j.e.a.AnnotationMBeanExporter - message:Located managed bean 'environmentManager': registering with JMX server as MBean [org.springframework.cloud.context.environment:name=environmentManager,type=EnvironmentManager]
2025-08-18 17:34:58.256    [main] DEBUG logger_name:o.s.j.e.a.AnnotationMBeanExporter - message:Located managed bean 'rabbitAdmin': registering with JMX server as MBean [org.springframework.amqp.rabbit.core:name=rabbitAdmin,type=RabbitAdmin]
2025-08-18 17:34:58.277    [main] DEBUG logger_name:o.s.j.e.a.AnnotationMBeanExporter - message:Located managed bean 'refreshScope': registering with JMX server as MBean [org.springframework.cloud.context.scope.refresh:name=refreshScope,type=RefreshScope]
2025-08-18 17:34:58.299    [main] DEBUG logger_name:o.s.j.e.a.AnnotationMBeanExporter - message:Located managed bean 'integrationMbeanExporter': registering with JMX server as MBean [org.springframework.integration.monitor:name=integrationMbeanExporter,type=IntegrationMBeanExporter]
2025-08-18 17:34:58.349    [main] DEBUG logger_name:o.s.j.e.a.AnnotationMBeanExporter - message:Located managed bean 'configurationPropertiesRebinder': registering with JMX server as MBean [org.springframework.cloud.context.properties:name=configurationPropertiesRebinder,context=4e6f2bb5,type=ConfigurationPropertiesRebinder]
2025-08-18 17:34:58.358    [main] DEBUG logger_name:o.s.j.e.a.AnnotationMBeanExporter - message:Located MBean 'dataSource': registering with JMX server as MBean [com.zaxxer.hikari:name=dataSource,type=HikariDataSource]
2025-08-18 17:34:58.360    [main] DEBUG logger_name:o.s.j.e.a.AnnotationMBeanExporter - message:Located managed bean 'rabbitConnectionFactory': registering with JMX server as MBean [org.springframework.amqp.rabbit.connection:name=rabbitConnectionFactory,type=CachingConnectionFactory]
2025-08-18 17:34:58.395    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'inMemorySwaggerResourcesProvider-out-0'
2025-08-18 17:34:58.947    [main] DEBUG logger_name:o.s.c.s.DefaultLifecycleProcessor - message:Starting beans in phase -**********
2025-08-18 17:34:58.948    [main] DEBUG logger_name:o.s.c.s.DefaultLifecycleProcessor - message:Successfully started bean '_org.springframework.integration.errorLogger'
2025-08-18 17:34:58.948    [main] DEBUG logger_name:o.s.c.s.DefaultLifecycleProcessor - message:Successfully started bean 'inMemorySwaggerResourcesProvider_integrationflow.org.springframework.integration.config.ConsumerEndpointFactoryBean#0'
2025-08-18 17:34:58.948    [main] DEBUG logger_name:o.s.c.s.DefaultLifecycleProcessor - message:Starting beans in phase -**********
2025-08-18 17:34:58.955    [main] DEBUG logger_name:o.s.c.a.AnnotationConfigApplicationContext - message:Refreshing rabbit_context
2025-08-18 17:34:58.955    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.context.annotation.internalConfigurationAnnotationProcessor'
2025-08-18 17:34:59.030    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'propertySourcesPlaceholderConfigurer'
2025-08-18 17:34:59.030    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.context.event.internalEventListenerProcessor'
2025-08-18 17:34:59.030    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.context.event.internalEventListenerFactory'
2025-08-18 17:34:59.030    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.context.annotation.internalAutowiredAnnotationProcessor'
2025-08-18 17:34:59.031    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.context.annotation.internalCommonAnnotationProcessor'
2025-08-18 17:34:59.032    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor'
2025-08-18 17:34:59.032    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.context.internalConfigurationPropertiesBinder'
2025-08-18 17:34:59.032    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.context.internalConfigurationPropertiesBinderFactory'
2025-08-18 17:34:59.032    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'rabbitBinderConfiguration'
2025-08-18 17:34:59.032    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'spelExpressionConverterConfiguration'
2025-08-18 17:34:59.032    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.stream.binder.rabbit.config.RabbitConfiguration'
2025-08-18 17:34:59.032    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.context.properties.BoundConfigurationProperties'
2025-08-18 17:34:59.032    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.context.properties.EnableConfigurationPropertiesRegistrar.methodValidationExcludeFilter'
2025-08-18 17:34:59.032    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.stream.binder.rabbit.config.RabbitBinderConfiguration$NoCloudProfile'
2025-08-18 17:34:59.032    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.context.PropertyPlaceholderAutoConfiguration'
2025-08-18 17:34:59.032    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'org.springframework.cloud.stream.binder.rabbit.config.RabbitMessageChannelBinderConfiguration'
2025-08-18 17:34:59.034    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'spring.cloud.stream.rabbit.binder-org.springframework.cloud.stream.binder.rabbit.properties.RabbitBinderConfigurationProperties'
2025-08-18 17:34:59.036    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'spelConverter'
2025-08-18 17:34:59.037    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Autowiring by type from bean name 'spelConverter' via factory method to bean named 'org.springframework.context.annotation.AnnotationConfigApplicationContext@17163282'
2025-08-18 17:34:59.042    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'spring.cloud.stream.rabbit-org.springframework.cloud.stream.binder.rabbit.properties.RabbitExtendedBindingProperties'
2025-08-18 17:34:59.047    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'rabbitMessageChannelBinder'
2025-08-18 17:34:59.097    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'provisioningProvider'
2025-08-18 17:34:59.105    [main] DEBUG logger_name:o.s.c.s.GenericApplicationContext - message:Refreshing org.springframework.context.support.GenericApplicationContext@410126b8
2025-08-18 17:34:59.105    [main] DEBUG logger_name:o.s.c.e.PropertySourcesPropertyResolver - message:Found key 'spring.liveBeansView.mbeanDomain' in PropertySource 'systemProperties' with value of type String
2025-08-18 17:34:59.131    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'gZipPostProcessor'
2025-08-18 17:34:59.132    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'deCompressingPostProcessor'
2025-08-18 17:34:59.149    [main] DEBUG logger_name:o.s.b.f.s.DefaultListableBeanFactory - message:Creating shared instance of singleton bean 'spelPropertyAccessorRegistrar'
2025-08-18 17:34:59.151    [main] DEBUG logger_name:o.s.c.e.PropertySourcesPropertyResolver - message:Found key 'spring.liveBeansView.mbeanDomain' in PropertySource 'systemProperties' with value of type String
2025-08-18 17:35:05.554    [cluster-ClusterId{value='68a2f3ab92e95743146149d3', description='null'}-localhost:27017] DEBUG logger_name:org.mongodb.driver.cluster - message:Updating cluster description to  {type=STANDALONE, servers=[{address=localhost:27017, type=STANDALONE, roundTripTime=82.3 ms, state=CONNECTED}]
2025-08-18 17:35:05.554    [cluster-ClusterId{value='68a2f3ab92e95743146149d3', description='null'}-localhost:27017] DEBUG logger_name:org.mongodb.driver.cluster - message:Checking status of localhost:27017
2025-08-18 17:35:05.587    [cluster-rtt-ClusterId{value='68a2f3ab92e95743146149d3', description='null'}-localhost:27017] DEBUG logger_name:org.mongodb.driver.protocol.command - message:Sending command '{"hello": 1, "$db": "admin", "$readPreference": {"mode": "primaryPreferred"}}' with request id 6 to database admin on connection [connectionId{localValue:1, serverValue:22}] to server localhost:27017
2025-08-18 17:35:05.588    [cluster-rtt-ClusterId{value='68a2f3ab92e95743146149d3', description='null'}-localhost:27017] DEBUG logger_name:org.mongodb.driver.protocol.command - message:Execution of command with request id 6 completed successfully in 1.66 ms on connection [connectionId{localValue:1, serverValue:22}] to server localhost:27017
2025-08-18 17:35:15.554    [cluster-ClusterId{value='68a2f3ab92e95743146149d3', description='null'}-localhost:27017] DEBUG logger_name:org.mongodb.driver.cluster - message:Updating cluster description to  {type=STANDALONE, servers=[{address=localhost:27017, type=STANDALONE, roundTripTime=66.3 ms, state=CONNECTED}]
2025-08-18 17:35:15.554    [cluster-ClusterId{value='68a2f3ab92e95743146149d3', description='null'}-localhost:27017] DEBUG logger_name:org.mongodb.driver.cluster - message:Checking status of localhost:27017
2025-08-18 17:35:15.588    [cluster-rtt-ClusterId{value='68a2f3ab92e95743146149d3', description='null'}-localhost:27017] DEBUG logger_name:org.mongodb.driver.protocol.command - message:Sending command '{"hello": 1, "$db": "admin", "$readPreference": {"mode": "primaryPreferred"}}' with request id 7 to database admin on connection [connectionId{localValue:1, serverValue:22}] to server localhost:27017
2025-08-18 17:35:15.589    [cluster-rtt-ClusterId{value='68a2f3ab92e95743146149d3', description='null'}-localhost:27017] DEBUG logger_name:org.mongodb.driver.protocol.command - message:Execution of command with request id 7 completed successfully in 0.71 ms on connection [connectionId{localValue:1, serverValue:22}] to server localhost:27017
