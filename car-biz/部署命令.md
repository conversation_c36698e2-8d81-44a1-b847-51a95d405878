#进入car.jar目录
cd /data/car/
#查询jar运行端口
ps -ef|grep car-biz.jar
#运行jar
nohup java -Dspring.cloud.nacos.config.access-key=LTAI5tJbV8oRChFDbsJx92vS   -Dspring.cloud.nacos.config.secret-key=******************************  -Dspring.cloud.nacos.discovery.access-key=LTAI5tJbV8oRChFDbsJx92vS  -Dspring.cloud.nacos.discovery.secret-key=******************************     -Dspring.cloud.nacos.config.namespace=6b561e72-019c-4bd3-9aaa-2115a7ac327c   -Dspring.cloud.nacos.discovery.server-addr=mse-cd6609313-nacos-ans.mse.aliyuncs.com -Dspring.cloud.nacos.discovery.namespace=6b561e72-019c-4bd3-9aaa-2115a7ac327c  -Dspring.cloud.nacos.discovery.ip=************ -jar car-biz.jar -> car.log &
#查看log日志
tail -f car.log
