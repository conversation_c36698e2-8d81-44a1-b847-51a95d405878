<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bctools.modules.paper.mapper.PaperMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.bctools.modules.paper.entity.Paper">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="depart_id" property="departId" />
        <result column="exam_id" property="examId" />
        <result column="title" property="title" />
        <result column="total_time" property="totalTime" />
        <result column="user_time" property="userTime" />
        <result column="total_score" property="totalScore" />
        <result column="qualify_score" property="qualifyScore" />
        <result column="obj_score" property="objScore" />
        <result column="subj_score" property="subjScore" />
        <result column="user_score" property="userScore" />
        <result column="has_saq" property="hasSaq" />
        <result column="state" property="state" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="limit_time" property="limitTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        `id`,`user_id`,`depart_id`,`exam_id`,`title`,`total_time`,`user_time`,`total_score`,`qualify_score`,`obj_score`,`subj_score`,`user_score`,`has_saq`,`state`,`create_time`,`update_time`,`limit_time`
    </sql>


    <resultMap id="ListResultMap"
               extends="BaseResultMap"
               type="cn.bctools.modules.paper.dto.response.PaperListRespDTO">
        <result column="real_name" property="realName" />
    </resultMap>

    <select id="paging" resultMap="ListResultMap">
        SELECT pp.*,uc.real_name FROM el_paper pp
        LEFT JOIN sys_user uc ON pp.user_id=uc.id
        <where>
            <if test="query!=null">
                <if test="query.examId!=null and query.examId!=''">
                    AND pp.exam_id=#{query.examId}
                </if>

                <if test="query.userId!=null and query.userId!=''">
                    AND pp.user_id=#{query.userId}
                </if>

                <if test="query.departId!=null and query.departId!=''">
                    AND pp.depart_id=#{query.departId}
                </if>

                <if test="query.state!=null">
                    AND pp.state=#{query.state}
                </if>
                <if test="query.realName!=null and query.realName!=''">
                    AND uc.real_name  LIKE CONCAT('%',#{query.realName},'%')
                </if>
            </if>
        </where>

        ORDER BY create_time DESC
    </select>

</mapper>
