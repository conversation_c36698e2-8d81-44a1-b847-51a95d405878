server:
  port: ${random.int[50001,50099]}
version: @project.version@
spring:
  application:
    name: @project.artifactId@
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  cloud:
    nacos:
      discovery:
        server-addr: http://www.bctools.cn
        group: jvs
        namespace: 6b561e72-019c-4bd3-9aaa-2115a7ac327c
      config:
        server-addr: mse-cd6609313-p.nacos-ans.mse.aliyuncs.com:8848
        group: jvs
        namespace: 6b561e72-019c-4bd3-9aaa-2115a7ac327c
    inetutils:
      #选择使用此网段进行处理
      preferred-networks: 10.*
  config:
    import:
      #公共配置
      - optional:nacos:application.yml
      #公共配置项目配置
      - optional:nacos:${spring.application.name}-application.yml
      #父级配置
      - optional:nacos:${project.parent.artifactId}.yml
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
swagger:
  title: "示例项目"
  description: "演示获取用户, 资源权限管理"
