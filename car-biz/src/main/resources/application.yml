thread.pool.queueCapacity: 2000
#是否开启登陆ip转换地址
ip:
  transition: true
server:
  port: 9002
  servlet:
    context-path: /biz
  netty:
    connection-timeout: 50000
  undertow:
    buffer-size: 512
    direct-buffers: true
    threads:
      io: 125
      worker: 2048
    no-request-timeout: 2000
    max-http-post-size: -1

spring:
  main:
    #是否允许循环依赖
    allow-circular-references: true
    #是否启动懒加载 与@Lazy 注解一起使用
    lazy-initialization: false
  mvc:
    pathmatch:
      #修改mvc的匹配策略
      matching-strategy: ant_path_matcher
  #是否开启缓存注解
  cache:
    type: none
    redis:
      #缓存前缀
      key-prefix: jvs::temp
  servlet:
    multipart:
      enabled: true
      #请求最大大小
      max-request-size: 20000MB
      #文件大小
      max-file-size: 2000MB
  task:
    execution:
      pool:
        core-size: 12
        max-size: 30
        queue-capacity: 200
        keep-alive: 60s
        allow-core-thread-timeout: true
      shutdown:
        await-termination: false
        await-termination-period: 30s
      thread-name-prefix: jvs-
  arthas:
    enabled: false
  rabbitmq:
    host: ************
    port: 5672
    username: jvs
    password: jvs
    # 使用其他环境时需要覆盖此属性
    virtual-host: /jvs
    listener:
      simple:
        acknowledge-mode: AUTO
  mongodb:
    database: jvs_data
    host: localhost
    port: 27017
    authentication-database: jvs_data
  redis:
    host: localhost
    port: 6379
    timeout: 100000
    database: 2
    lettuce:
      pool:
        #lettuce采用多路复用原理，因此真正工作的连接受制于CPU核数因此增大连接数反而增加了线程上下文切换时间。因此建议调整为  CPU核数+1.
        #连接池最大连接数（使用负值表示没有限制）
        max-active: 29
        #连接池中的最大空闲连接
        max-idle: 9
        #连接池中的最小空闲连接
        min-idle: 1
        #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1
  datasource:
    url: *************************************************************************************************************************************************************************************************************************
    username: root
    password: admin
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      read-only: false
      #客户端等待连接池连接的最大毫秒数
      connection-timeout: 30000
      #允许连接在连接池中空闲的最长时间(以毫秒为单位)
      # idle-timeout: 60000
      #连接将被测试活动的最大时间量
      validation-timeout: 30000
      #池中连接关闭后的最长生命周期
      max-lifetime: 30000
      #最大池大小
      # maximum-pool-size: 144
      # #连接池中维护的最小空闲连接数
      # minimum-idle: 40
      #从池返回的连接的默认自动提交行为。默认值为true
      auto-commit: true
      #如果您的驱动程序支持JDBC4，我们强烈建议您不要设置此属性
      # connection-test-query: SELECT 1
      #自定义连接池名称
      pool-name: MyHikariCP
  #接入网关限流sentinel
  cloud:
    sentinel:
      enable: true
      transport:
        #指定地址
        dashboard: jvs-sentinel:8084
      log:
        #日志文件
        dir: log/sentinel

  jackson:
    #配置json格式化
    default-property-inclusion: non_null

feign:
  okhttp:
    enabled: true
  httpclient:
    enabled: false
    max-connections: 1000
    max-connections-per-route: 100
  compression:
    request:
      mime-types: text/xml,application/xml,application/json
      min-request-size: 2048
      enabled: false
    response:
      enabled: true
  client:
    config:
      default:
        connectTimeout: 180000
        readTimeout: 180000

#日志配置,是否开启,和等级配置
logging:
  #使用logstash方式日志传递
  config: classpath:logback-logstash.xml
  level:
    #屏蔽 nacos 心跳日志,因为日志打印太频繁
    com.alibaba.nacos.client.naming: error
    #屏蔽MQ日志
    org.springframework.amqp: error
    org.springframework.integration: error
    org.springframework.cloud: error
    #redis
    io.lettuce.core: off
    org.apache.ibatis.logging: debug
    com.baomidou.mybatisplus: debug
    org.apache.http.wire: error
    org.eclipse: off
    # org.springframework: off
    cn.bctools: info
    net.logstash.logback: off
    com.zaxxer.hikari.pool.PoolBase: error
    com.zaxxer.hikari.pool.HikariPool: error

mybatis-plus:
  configuration:
    #开启Mybatis plus 二级缓存, 单表,修改和新增时操作.建议配合使用 @CacheNamespace(implementation = JvsRedisCache.class, eviction = JvsRedisCache.class)
    cache-enabled: false
  global-config:
    db-config:
      #全局逻辑删除字段值
      logic-delete-field: delFlag
  # 支持统配符 * 或者 ; 分割
  typeEnumsPackage: cn.bctools.**.entity.enums
    #  configuration:
    #   log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    # 关闭sql日志
    #log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl
  # 使用枚举的name值进行映射
#    default-enum-type-handler: ''

#文件存储内容
oss:
  #默认s3 支持minio  name : minio   建议使用minio最新版本，oss需要一个桶绑定一个域名， 否则前端使用时会导致图片无法显示
  name: s3
  endpoint: https://gyczcmn.cqtransit.com
  access-key: miniominio
  secret-key: miniominio
  #声明公共桶，获取地址的时候直接为公有地址
  publicBuckets:
    #公共桶只有一个, 所有的都可以向这个上面传递, 但需要符合规范./项目/租户/功能名xxxx/xxx/xxx/ 三层,/当前年/月/日/文件名
    - jvs-public

#jvs 部分配置低代码使用到接入的kkfile地址
jvs:
  #模式,多租户模式, 默认为false
  multi-tenant-mode: true
  #服务的ip或域名,如果是多租户模式,则直接
  domain: cqtransit.com
  #是否是生产模式,某些功能是否走本地缓存,不配置默认为 false
  dev: true
  service:
    #后台服务
    - name: BACKGROUND_PERSONALIZED_CONFIGURATION
      port: 8088
    # #bi项目
    # - name: PERSONALIZED_CONFIGURATION_OF_DATA_INTELLIGENCE_WAREHOUSE
    #   port: 30011
    # #文档
    # - name: ENTERPRISE_DOCUMENT_ENTERPRISE_CONFIGURATION
    #   port: 30005
    # #项目管理
    # - name: PERSONALIZED_CONFIGURATION_OF_ENTERPRISE_PLANNING
    #   port: 31013
    # #企业由桶
    # - name: PERSONALIZED_CONFIGURATION_OF_EMAIL_SERVICE
    #   port: 32023
    # #规则
    # - name: RULE_PERSONALIZATION_CONFIGURATION
    #   port: 30004
    # #视频会议
    # - name: PERSONALIZED_CONFIGURATION_FOR_VIDEO_CONFERENCING
    #   port: 30011
    # #物联网
    # - name: PERSONALIZED_CONFIGURATION_IOT_PLATFORMS
    #   port: 30011
    # #逻辑引擎
    # - name: AUTOMATION_CONFIGURATION
    #   port: 32009
    #服务资源,用户配置界面中相互跳转有哪些服务
    # permission:
    #   - JVS_DESIGN_MGR
    #   - JVS_CHART_MGR
    #   - jvs_document_service
    #   - jvs_risk_service
    #   - jvs_teamwork_ultimate_service
  # 轻代码表单文件预览
  kkfileUrl: https://file.preview.bctools.cn
  other-login:
    # true-开启平台新用户登录自动注册；false-不开启自动注册(默认)
    enable-automatic-register: true
  dingding:
    log: true
    secret: SEC5734023ae7d5d98a18cef67ae255642a06ba30e9124cd279213ac5251d26802d
    url: https://oapi.dingtalk.com/robot/send?access_token=b6a9772e92665993f568418a13fa806a44fa9f2925446dfa0572e40bbe3db183

# gateway Swagger文档开关,默认是false 开发环境设置为true 其它环境不用设置
swagger:
  enable: false


# Seata默认配置
seata:
  # 事务分组名称(与服务端一致)
  tx-service-group: my_test_tx_group
  # 获取Seata服务端ip:port的方式
  registry:
    type: nacos
    nacos:
      password: nacos
      username: nacos
      namespace: ${spring.cloud.nacos.discovery.namespace}
      server-addr: ${spring.cloud.nacos.discovery.server-addr}
  client:
    rm:
      # 获取全局锁相关配置
      lock:
        # 重试次数
        retry-times: 10
        # 每次等待时间(ms)
        retry-interval: 1000
        # 尝试获取全局锁失败时是否立即报错(false时会等待锁)
        retry-policy-branch-rollback-on-conflict: false
#钉钉AOP接口超时异常通知
log:
  dingding:
    log: false
    secret: SEC5734023ae7d5d98a18cef67ae255642a06ba30e9124cd279213ac5251d26802d
    url: https://oapi.dingtalk.com/robot/send?access_token=b6a9772e92665993f568418a13fa806a44fa9f2925446dfa0572e40bbe3db183


wx:
  # 微信小程序
  miniapp:
    appid: wx6f24be89a5f4511c
    secret: 41275544401db6a8b338e97c83ff8e33
    config-storage:
      type: redistemplate
  # mp:
  #   appId: wx62c0d436af547522
  #   secret: 063028c20f54e587fe0d5932043288eb
  #   token: 72597b9628704ab09e8b9e8cbe9b540a
  #   aesKey: s9bIOa7xWo9XsoBx6R8zlYZ9cTCXN57FvOhObiIWLVV
#不使用默认首页
jvs.defaultIndex: false



#doris配置
doris:
  ip: jvs-doris
  queryPort: 9030
  httpPort: 8030
  passWord: rkqf2024!QAZ2wsx
  userName: root
  #需要连接的数据库 此数据库名称可以修改 名称修改后 需要重启服务 服务启动会自动创建数据库
  libraryName: jvs_data_factory_ods