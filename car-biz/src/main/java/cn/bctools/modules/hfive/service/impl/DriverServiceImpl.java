package cn.bctools.modules.hfive.service.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import cn.bctools.modules.hfive.dto.DriverInformation;
import cn.bctools.modules.hfive.dto.resp.DeiverUserAgreeResp;
import cn.bctools.modules.hfive.mapper.DriverMapper;
import cn.bctools.modules.hfive.mongoDto.deiver_user_agree;
import cn.bctools.modules.hfive.service.DriverService;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2023-12-05 16:03
 * @Version: 1.0
 */
@Service
public class DriverServiceImpl implements DriverService {
    @Resource
    DriverMapper driverMapper;
    @Resource
    MongoTemplate mongoTemplate;

    @Override
    public Boolean add(DriverInformation driverInformation) {
        driverInformation.setId(IdWorker.getIdStr());
        mongoTemplate.save(driverInformation);
        return true;
    }

    @Override
    public DriverInformation getOne(DriverInformation driverInformation) {
        Criteria regex = Criteria.where("_id").is(driverInformation.getId());
        Query query = new Query();
        query.addCriteria(regex);
        List<DriverInformation> resp = mongoTemplate.find(query, DriverInformation.class);
        return resp.get(0);
    }

    @Override
    public List<DeiverUserAgreeResp> getDeiverAgree() {
        List<DeiverUserAgreeResp> resps = new ArrayList<>();
        Query query = new Query();
        List<deiver_user_agree> resp = mongoTemplate.find(query, deiver_user_agree.class);
        if (resp.size() > 0) {
            deiver_user_agree deiverUserAgree = resp.get(0);
            //安全责任书
            deiverUserAgree.getAnQuanXingCheYouZhiFuWuZeRenShu();
            DeiverUserAgreeResp anQuanXingCheYouZhiFuWuZeRenShu=new DeiverUserAgreeResp();
            anQuanXingCheYouZhiFuWuZeRenShu.setId(deiverUserAgree.getId());
            anQuanXingCheYouZhiFuWuZeRenShu.setType("anQuanXingCheYouZhiFuWuZeRenShu");
            anQuanXingCheYouZhiFuWuZeRenShu.setContent(deiverUserAgree.getAnQuanXingCheYouZhiFuWuZeRenShu());
            resps.add(anQuanXingCheYouZhiFuWuZeRenShu);
            //安全承诺书
            deiverUserAgree.getAnQuanChengNuoShu();
            DeiverUserAgreeResp anQuanChengNuoShu=new DeiverUserAgreeResp();
            anQuanChengNuoShu.setId(deiverUserAgree.getId());
            anQuanChengNuoShu.setType("getAnQuanChengNuoShu");
            anQuanChengNuoShu.setContent(deiverUserAgree.getAnQuanChengNuoShu());
            resps.add(anQuanChengNuoShu);
            //安全操作规程
            deiverUserAgree.getAnQuanCaoZuoGuiCheng();
            DeiverUserAgreeResp anQuanCaoZuoGuiCheng=new DeiverUserAgreeResp();
            anQuanCaoZuoGuiCheng.setId(deiverUserAgree.getId());
            anQuanCaoZuoGuiCheng.setType("getAnQuanCaoZuoGuiCheng");
            anQuanCaoZuoGuiCheng.setContent(deiverUserAgree.getAnQuanCaoZuoGuiCheng());
            resps.add(anQuanCaoZuoGuiCheng);
            //安全生产责任制
            deiverUserAgree.getAnQuanShengChanZeRenZhi();
            DeiverUserAgreeResp anQuanShengChanZeRenZhi=new DeiverUserAgreeResp();
            anQuanShengChanZeRenZhi.setId(deiverUserAgree.getId());
            anQuanShengChanZeRenZhi.setType("anQuanShengChanZeRenZhi");
            anQuanShengChanZeRenZhi.setContent(deiverUserAgree.getAnQuanShengChanZeRenZhi());
            resps.add(anQuanShengChanZeRenZhi);
            //驾驶协议
            deiverUserAgree.getJiaShiYuanXieYi();
            DeiverUserAgreeResp jiaShiYuanXieYi=new DeiverUserAgreeResp();
            jiaShiYuanXieYi.setId(deiverUserAgree.getId());
            jiaShiYuanXieYi.setType("getJiaShiYuanXieYi");
            jiaShiYuanXieYi.setContent(deiverUserAgree.getJiaShiYuanXieYi());
            resps.add(jiaShiYuanXieYi);

            return resps;
        } else {
            return new ArrayList<>();
        }
    }
}
