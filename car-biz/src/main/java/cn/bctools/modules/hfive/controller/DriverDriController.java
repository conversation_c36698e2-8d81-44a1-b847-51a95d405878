package cn.bctools.modules.hfive.controller;

import cn.bctools.common.utils.R;
import cn.bctools.modules.hfive.dto.DriverInformation;
import cn.bctools.modules.hfive.service.DriverService;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2023-12-05 15:59
 * @Version: 1.0
 */
@RestController
@RequestMapping("dri")
public class DriverDriController {
    @Resource
    DriverService driverService;
    @PostMapping("add")
    @ApiOperation(value = "add" , notes = "新增企业基本信息")
    public R add(@RequestBody DriverInformation driverInformation){
        Boolean status = driverService.add(driverInformation);
        if (!status){
            return R.failed();
        }
        return new R();
    }
    @PostMapping("getOne")
    public R getList(@RequestBody DriverInformation driverInformation){

        return R.ok(driverService.getOne(driverInformation));
    }

    @PostMapping("getDeiverAgree")
    public R getDeiverAgree(){

        return R.ok(driverService.getDeiverAgree());
    }
}
