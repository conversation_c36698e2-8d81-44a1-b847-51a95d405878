package cn.bctools.modules.hfive.service;

import cn.bctools.modules.hfive.dto.DriverInformation;
import cn.bctools.modules.hfive.dto.resp.DeiverUserAgreeResp;

import java.util.List;

/**
 * @Author: <PERSON><PERSON>yin
 * @Date: Created in 2023-12-05 16:02
 * @Version: 1.0
 */
public interface DriverService {

    Boolean add(DriverInformation driverInformation);

    DriverInformation getOne(DriverInformation driverInformation);

    List<DeiverUserAgreeResp> getDeiverAgree();

}
