package cn.bctools.modules.hfive.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2023-12-05 16:07
 * @Version: 1.0
 */
@Data
public class DriverInformation implements Serializable {

    private static final long serialVersionUID = 7523059264202861246L;

    private String id;
    //手写签名
    private String signature1697707123646;
    //附件
    private String fuJian;
    //头像
    private String touXiang;
    //工作流状态
    private String jvsFlowTaskState;
    //交运集团驾驶人黑名单查询
    private String jiaoYunJiTuanJiaShiRenHeiMingDanChaXun;
    //死亡事故同等以上责任查询
    private String siWangShiGuTongDengYiShangZeRenChaXun;
    //计分查询
    private String jiFenChaXun;
    //违法信息查询
    private String weiFaXinXiChaXun;
    //从业证有效期限
    private String congYeZhengYouXiaoQiXian;
    //驾驶证有效期限
    private String jiaShiZhengYouXiaoQiXian;
    //从业类别
    private String congYeLeiBie;
    //从业证初次领证时间
    private String congYeZhengChuCiLingZhengShiJian;
    //从业证号
    private String congYeZhengHao;
    //驾驶证初次领证时间
    private String jiaShiZhengChuCiLingZhengShiJian;
    //准驾车型
    private String zhunJiaCheXing;
    //车队名称
    private String cheDuiMingChen;
    //姓名
    private String xingMing;
    //性别
    private String xingBie;
    //出生年月
    private String chuShengNianYue;
    //家庭住址
    private String jiaTingZhuZhi;
    //身份证号
    private String shenFenZhengHao;
    //驾驶证号
    private String jiaShiZhengHao;
    //车队
    private String cheDui;
    //驾驶员简历列表
    private String tableForm1684290277303;

}
