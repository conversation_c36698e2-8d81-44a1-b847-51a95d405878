package cn.bctools.modules.datav.dto;

import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@Document(collection  = "taix_payroll")
public class WagesVo {
    public String shenFenLeiBie;
    public String shangGangGongZi;
    public String zhuJiaGangWeiBuTie;
    public String yueDuKaoHe;
    public String shangGangBuTie;
    public String gaoWenBuTie;
    public String jiDuKaoHe;
    public String yingFaXiaoJi;
    public String yangLaoBaoXian;
    public String yiLiaoBaoXian;
    public String shiYeBaoXian;
    public String geShui;
    public String kaoHe;
    public String shiFaJinE;
}
