package cn.bctools.modules.datav.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("t_subscribe_all")
public class SubscribeAll {
    @TableId
    private Long id;
    private Double income;
    private String mdtId;
    private String carNo;
    private String companyName;
    private String createTime;
}
