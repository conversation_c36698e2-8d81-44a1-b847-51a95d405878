package cn.bctools.modules.datav.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import cn.bctools.modules.datav.dto.SubscribeDto;
import cn.bctools.modules.datav.dto.SubscribeReq;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface SubscribeMapper extends BaseMapper<SubscribeReq> {

    List<SubscribeDto> getListByDate(String dept);

    List<SubscribeDto> getListAll();
}
