package cn.bctools.modules.datav.utils;

import cn.hutool.core.codec.Base64;
import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.symmetric.AES;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.nio.charset.StandardCharsets;


/**
 * @Author: Zhanghongyin
 * @Date: Created in 2023-12-14 15:35
 * @Version: 1.0
 */
public class CreateQuery {

    public static Query createCriteria(String select, Object price) {
        Criteria criteria=new Criteria();
        Criteria regex = Criteria.where(select).is(price);
        criteria.andOperator(regex);
        Query query=new Query();
        query.addCriteria(criteria);
        return query;
    }

    public static void main(String[] args) {
        String ss = decodedPassword(
                "94c3bfb54a73b112f805b28af96bb948");
        System.out.println(ss);
    }
    /**
     * 解密
     *
     * @param encodedPassword 密文
     * @return 明文
     */
    public static String decodedPassword(String encodedPassword) {
        try {
            AES aes = getKey();
            return aes.decryptStr(encodedPassword, StandardCharsets.UTF_8).trim();
        } catch (Exception e) {
            throw new RuntimeException("解密失败");
        }
    }
    /**
     * 替换Key通过APPID获取前端加密key
     *
     * @return
     */
    private static AES getKey() {

        String key = Base64.encode("jvs");
        //超过16位，截取前面 16位
        int i = 16;
        if (key.length() >= i) {
            key = key.substring(0, i);
        }
        String format = String.format("%016d", 0);
        key += format.substring(key.length());
        byte[] bytes = key.getBytes(StandardCharsets.UTF_8);
        AES aes = new AES(Mode.CBC, Padding.ZeroPadding, bytes, bytes);

        return aes;
    }
}
