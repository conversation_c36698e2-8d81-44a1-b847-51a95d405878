package cn.bctools.modules.datav.service;

import cn.bctools.modules.datav.dto.*;
import cn.bctools.modules.datav.dto.req.DataReq;
import cn.bctools.modules.datav.dto.req.WagesReq;
import cn.bctools.modules.datav.dto.resp.DriverDepartResp;
import cn.bctools.modules.datav.dto.resp.InspectionResp;
import cn.bctools.modules.datav.dto.resp.SummaryResp;

import java.util.List;

public interface DataBoardService {

    List<SummaryResp> getSummary(DataReq dataReq);


    DriverDepartResp getCarYear(DataReq dataReq);


    DriverDepartResp getDriverYear(DataReq dataReq);

    DriverDepartResp getComplaint(DataReq dataReq);

    DriverDepartResp getMoney();

//    DriverDepartResp getHidden(DataReq dataReq);

    DriverDepartResp getAccident(DataReq dataReq);

    List<SummaryResp> getHonor(DataReq dataReq);

    WagesResp getWagesList(WagesReq wagesReq);

    List<HiddenDtoNew> getHiddenNew(DataReq dataReq);

    List<SummaryResp> getDriverAge(DataReq dataReq);

    InspectionResp monthInspection(DataReq dataReq);

    DriverDepartResp onLine(DataReq dataReq);

    List<CarTypeNumDto> carType(DataReq dataReq);

    DriverDepartResp getChuZuPeople();

    Boolean saveSubscribe(SubscribeReq subscribeReq);
}
