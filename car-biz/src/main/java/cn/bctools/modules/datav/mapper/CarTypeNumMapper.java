package cn.bctools.modules.datav.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import cn.bctools.modules.datav.dto.CarTypeNumDto;
import cn.bctools.modules.datav.job.CarTypeNumEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface CarTypeNumMapper extends BaseMapper<CarTypeNumEntity> {
    List<CarTypeNumDto> getCarTypeNum(String deptCode);
}
