package cn.bctools.modules.datav.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import cn.bctools.modules.datav.dto.DriverInfoDayNumEntity;
import cn.bctools.modules.datav.dto.DriverInfoDayNumResp;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface DriverInfoDayNumMapper extends BaseMapper<DriverInfoDayNumEntity> {
    List<DriverInfoDayNumResp> selectByDeptId(String deptCode);
}
