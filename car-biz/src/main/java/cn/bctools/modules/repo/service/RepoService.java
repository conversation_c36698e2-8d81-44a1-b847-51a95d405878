package cn.bctools.modules.repo.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import cn.bctools.core.api.dto.PagingReqDTO;
import cn.bctools.modules.repo.dto.RepoDTO;
import cn.bctools.modules.repo.dto.request.RepoReqDTO;
import cn.bctools.modules.repo.dto.response.RepoRespDTO;
import cn.bctools.modules.repo.entity.Repo;

/**
* <p>
* 题库业务类
* </p>
*
* <AUTHOR>
* @since 2020-05-25 13:23
*/
public interface RepoService extends IService<Repo> {

    /**
    * 分页查询数据
    * @param reqDTO
    * @return
    */
    IPage<RepoRespDTO> paging(PagingReqDTO<RepoReqDTO> reqDTO);


    /**
     * 保存
     * @param reqDTO
     */
    void save(RepoDTO reqDTO);
}
