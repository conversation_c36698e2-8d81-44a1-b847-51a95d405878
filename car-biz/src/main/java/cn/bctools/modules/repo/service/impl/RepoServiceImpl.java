package cn.bctools.modules.repo.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.bctools.core.api.dto.PagingReqDTO;
import cn.bctools.core.utils.BeanMapper;
import cn.bctools.modules.repo.dto.RepoDTO;
import cn.bctools.modules.repo.dto.request.RepoReqDTO;
import cn.bctools.modules.repo.dto.response.RepoRespDTO;
import cn.bctools.modules.repo.entity.Repo;
import cn.bctools.modules.repo.mapper.RepoMapper;
import cn.bctools.modules.repo.service.RepoService;
import org.springframework.stereotype.Service;

/**
* <p>
* 语言设置 服务实现类
* </p>
*
* <AUTHOR>
* @since 2020-05-25 13:23
*/
@Service
public class RepoServiceImpl extends ServiceImpl<RepoMapper, Repo> implements RepoService {

    @Override
    public IPage<RepoRespDTO> paging(PagingReqDTO<RepoReqDTO> reqDTO) {
        return baseMapper.paging(reqDTO.toPage(), reqDTO.getParams());
     }

    @Override
    public void save(RepoDTO reqDTO) {

        //复制参数
        Repo entity = new Repo();
        BeanMapper.copy(reqDTO, entity);
        this.saveOrUpdate(entity);
    }
}
