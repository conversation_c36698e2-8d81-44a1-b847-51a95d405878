package cn.bctools.modules.sys.user.service.impl;

import cn.bctools.ability.shiro.jwt.JwtUtils;
import cn.bctools.core.api.ApiError;
import cn.bctools.core.exception.ServiceException;
import cn.bctools.core.utils.BeanMapper;
import cn.bctools.modules.sys.user.dto.response.SysUserLoginDTO;
import cn.bctools.modules.sys.user.entity.MongoUser;
import cn.bctools.modules.sys.user.service.MongoUserService;
import cn.bctools.modules.sys.user.service.SysUserRoleService;
import cn.bctools.modules.sys.user.service.CaptchaService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * MongoDB用户服务实现类
 */
@Slf4j
@Service
public class MongoUserServiceImpl implements MongoUserService {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private SysUserRoleService sysUserRoleService;

    @Autowired(required = false)
    private CaptchaService captchaService;

    @Override
    public SysUserLoginDTO login(String yongHuZhangHu, String miMa, String captcha,String key) {
        
        // 验证参数
        if (StringUtils.isBlank(yongHuZhangHu)) {
            throw new ServiceException(ApiError.ERROR_10010001);
        }
        if (StringUtils.isBlank(miMa)) {
            throw new ServiceException(ApiError.ERROR_10010001);
        }

        // 验证验证码（如果提供了验证码）
        if (StringUtils.isNotBlank(captcha) && !verifyCaptcha(key,captcha)) {
            throw new ServiceException(ApiError.ERROR_10010012);
        }

        // 查找用户
        MongoUser user = findByYongHuZhangHu(yongHuZhangHu);
        if (user == null) {
            throw new ServiceException(ApiError.ERROR_90010001);
        }

        // 检查用户是否被删除
        if (user.getDelFlag() != null && user.getDelFlag()) {
            throw new ServiceException(ApiError.ERROR_90010005);
        }

        // 验证密码
        if (!verifyPassword(miMa, user.getMiMa())) {
            throw new ServiceException(ApiError.ERROR_90010002);
        }

        // 生成登录响应
        return generateLoginResponse(user);
    }

    @Override
    public MongoUser findByYongHuZhangHu(String yongHuZhangHu) {
        try {
            log.info("开始查询用户，yongHuZhangHu: {}", yongHuZhangHu);

            // 先测试连接和集合是否存在
            String collectionName = "444d5fec7d57c38f8e270f028acfca9748d469";
            boolean exists = mongoTemplate.collectionExists(collectionName);
            log.info("集合 {} 是否存在: {}", collectionName, exists);

            if (!exists) {
                log.error("MongoDB集合不存在: {}", collectionName);
                return null;
            }

            // 查询总数
            long count = mongoTemplate.count(new Query(), collectionName);
            log.info("集合 {} 总记录数: {}", collectionName, count);

            // 构建查询
            Query query = new Query();
            query.addCriteria(Criteria.where("yongHuZhangHu").is(yongHuZhangHu));

            log.info("执行查询，条件: yongHuZhangHu = {}", yongHuZhangHu);
            MongoUser user = mongoTemplate.findOne(query, MongoUser.class, collectionName);

            if (user != null) {
                log.info("找到用户: id={}, yongHuXingMing={}", user.getId(), user.getYongHuXingMing());
            } else {
                log.warn("未找到用户，yongHuZhangHu: {}", yongHuZhangHu);

                // 尝试查询所有记录的yongHuZhangHu字段，用于调试
                Query debugQuery = new Query();
                debugQuery.fields().include("yongHuZhangHu");
                debugQuery.limit(5);

                List<MongoUser> allUsers = mongoTemplate.find(debugQuery, MongoUser.class, collectionName);
                log.info("前5条记录的yongHuZhangHu字段:");
                for (MongoUser u : allUsers) {
                    log.info("  - yongHuZhangHu: '{}'", u.getYongHuZhangHu());
                }
            }

            return user;

        } catch (Exception e) {
            log.error("查询用户时发生异常", e);
            return null;
        }
    }

    @Override
    public boolean verifyPassword(String inputPassword, String storedPassword) {
        // 这里假设密码是明文存储的，根据您的实际情况调整
        // 如果密码是加密的，需要使用相应的解密或哈希验证方法
        return StringUtils.equals(inputPassword, storedPassword);
    }

    @Override
    public boolean verifyCaptcha(String key,String captcha) {
        // 如果没有提供验证码，则跳过验证
        if (StringUtils.isBlank(captcha)) {
            return true;
        }

        // 如果注入了验证码服务，使用验证码服务验证
        if (captchaService != null) {
            // 这里需要sessionId，实际使用时可能需要从请求中获取
            // 暂时使用固定值，您需要根据实际情况调整
            return captchaService.verifyCaptcha(key, captcha);
        }

        // 简单的验证码验证逻辑（仅用于演示）
        // 实际项目中应该使用更安全的验证机制
        log.warn("使用简单验证码验证逻辑，生产环境请实现完整的验证码服务");
        return "1234".equals(captcha); // 简单示例，实际应该从缓存中获取
    }

    /**
     * 生成登录响应
     */
    private SysUserLoginDTO generateLoginResponse(MongoUser mongoUser) {
        SysUserLoginDTO loginDTO = new SysUserLoginDTO();
        
        // 映射基本信息
        loginDTO.setId(mongoUser.getId());
        loginDTO.setUserName(mongoUser.getYongHuZhangHu());
        loginDTO.setRealName(mongoUser.getYongHuXingMing());
        loginDTO.setDepartId(mongoUser.getBuMenid());
        
        // 解析时间
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            if (StringUtils.isNotBlank(mongoUser.getCreateTime())) {
                loginDTO.setCreateTime(sdf.parse(mongoUser.getCreateTime()));
            }
            if (StringUtils.isNotBlank(mongoUser.getUpdateTime())) {
                loginDTO.setUpdateTime(sdf.parse(mongoUser.getUpdateTime()));
            }
        } catch (ParseException e) {
            log.warn("时间解析失败: {}", e.getMessage());
        }

        // 设置默认状态为正常
        loginDTO.setState(1);
        
        // 生成Token
        String token = JwtUtils.sign(mongoUser.getYongHuZhangHu());
        loginDTO.setToken(token);

        // 设置默认角色（可以根据实际需求调整）
        List<String> roles = new ArrayList<>();
        roles.add("user"); // 默认用户角色
        loginDTO.setRoles(roles);
        loginDTO.setRoleIds("user");

        return loginDTO;
    }
}
