package cn.bctools.modules.sys.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import cn.bctools.modules.sys.user.entity.SysUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
* <p>
* 管理用户Mapper
* </p>
*
* <AUTHOR>
* @since 2020-04-13 16:57
*/
public interface SysUserMapper extends BaseMapper<SysUser> {

    Integer selectMaxVipNum();

    void updateNum(@Param("num") Integer i);
}
