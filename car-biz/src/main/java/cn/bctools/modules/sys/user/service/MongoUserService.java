package cn.bctools.modules.sys.user.service;

import cn.bctools.modules.sys.user.dto.response.SysUserLoginDTO;
import cn.bctools.modules.sys.user.entity.MongoUser;

/**
 * MongoDB用户服务接口
 */
public interface MongoUserService {

    /**
     * 根据用户账户和密码登录
     * @param yongHuZhangHu 用户账户
     * @param miMa 密码
     * @param captcha 验证码
     * @return 登录响应
     */
    SysUserLoginDTO login(String yongHuZhangHu, String miMa, String captcha,String key);

    /**
     * 根据用户账户查找用户
     * @param yongHuZhangHu 用户账户
     * @return 用户信息
     */
    MongoUser findByYongHuZhangHu(String yongHuZhangHu);

    /**
     * 验证密码
     * @param inputPassword 输入的密码
     * @param storedPassword 存储的密码
     * @return 是否匹配
     */
    boolean verifyPassword(String inputPassword, String storedPassword);

    /**
     * 验证验证码
     * @param captcha 验证码
     * @return 是否有效
     */
    boolean verifyCaptcha(String key,String captcha);
}
