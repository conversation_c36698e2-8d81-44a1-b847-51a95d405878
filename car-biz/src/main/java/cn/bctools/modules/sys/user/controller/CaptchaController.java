package cn.bctools.modules.sys.user.controller;

import cn.bctools.core.api.ApiRest;
import cn.bctools.core.api.controller.BaseController;
import cn.bctools.modules.sys.user.service.CaptchaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.Serializable;

/**
 * 验证码控制器
 */
@Api(value = "验证码管理", tags = "验证码管理")
@RestController
@RequestMapping("/exam/api/captcha")
public class CaptchaController extends BaseController {

    @Autowired(required = false)
    private CaptchaService captchaService;

    /**
     * 获取验证码
     */
    @CrossOrigin
    @ApiOperation(value = "获取验证码", notes = "获取图形验证码")
    @RequestMapping(value = "/get", method = {RequestMethod.GET, RequestMethod.POST})
    public void captcha(HttpServletRequest request, HttpServletResponse response) throws IOException {
        captchaService.generateCaptcha(request, response);
    }
}
