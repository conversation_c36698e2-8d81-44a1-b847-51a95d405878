package cn.bctools.modules.sys.user.controller;

import cn.bctools.modules.sys.user.service.CaptchaService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import cn.bctools.core.api.ApiRest;
import cn.bctools.core.api.controller.BaseController;
import cn.bctools.core.api.dto.BaseIdsReqDTO;
import cn.bctools.core.api.dto.BaseStateReqDTO;
import cn.bctools.core.api.dto.PagingReqDTO;
import cn.bctools.modules.sys.user.dto.SysUserDTO;
import cn.bctools.modules.sys.user.dto.request.SysUserLoginReqDTO;
import cn.bctools.modules.sys.user.dto.request.SysUserSaveReqDTO;
import cn.bctools.modules.sys.user.dto.response.SysUserLoginDTO;
import cn.bctools.modules.sys.user.entity.SysUser;
import cn.bctools.modules.sys.user.service.SysUserService;
import cn.bctools.modules.sys.user.service.MongoUserService;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.bson.Document;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <p>
 * 管理用户控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-13 16:57
 */
@Api(value = "管理用户",tags="管理用户")
@RestController
@RequestMapping("/exam/api/sys/user")
public class SysUserController extends BaseController {

    @Autowired
    private SysUserService baseService;

    @Autowired
    private MongoUserService mongoUserService;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired(required = false)
    private CaptchaService captchaService;

    /**
     * 获取验证码
     */
    @CrossOrigin
    @ApiOperation(value = "获取验证码", notes = "获取图形验证码")
    @RequestMapping(value = "/get-captcha", method = {RequestMethod.GET, RequestMethod.POST})
    public void captcha(HttpServletRequest request, HttpServletResponse response) throws IOException {
        captchaService.generateCaptcha(request, response);
    }
    /**
     * 用户登录
     * @return
     */
    @CrossOrigin
    @ApiOperation(value = "用户登录")
    @RequestMapping(value = "/login", method = {RequestMethod.POST})
    public ApiRest<SysUserLoginDTO> login(@RequestBody SysUserLoginReqDTO reqDTO) {
        SysUserLoginDTO respDTO = baseService.login(reqDTO.getUsername(), reqDTO.getPassword());
        return super.success(respDTO);
    }

    /**
     * MongoDB用户登录
     * @param reqDTO 登录请求参数
     * @return 登录响应
     */
    @CrossOrigin
    @ApiOperation(value = "MongoDB用户登录", notes = "基于MongoDB存储的用户登录，支持验证码验证")
    @RequestMapping(value = "/mongo-login", method = {RequestMethod.POST})
    public ApiRest<SysUserLoginDTO> mongoLogin(@RequestBody SysUserLoginReqDTO reqDTO) {
        SysUserLoginDTO respDTO = mongoUserService.login(reqDTO.getUsername(), reqDTO.getPassword(), reqDTO.getCaptcha(), reqDTO.getKey());
        return super.success(respDTO);
    }

    /**
     * 测试MongoDB用户查询
     * @param username 用户账户
     * @return 用户信息
     */
    @CrossOrigin
    @ApiOperation(value = "测试MongoDB用户查询", notes = "用于调试MongoDB用户查询问题")
    @RequestMapping(value = "/test-mongo-user", method = {RequestMethod.GET})
    public ApiRest testMongoUser(@RequestParam String username) {
        try {
            cn.bctools.modules.sys.user.entity.MongoUser user = mongoUserService.findByYongHuZhangHu(username);
            if (user != null) {
                return super.success(user);
            } else {
                return super.failure("未找到用户: " + username);
            }
        } catch (Exception e) {
            return super.failure("查询异常: " + e.getMessage());
        }
    }

    /**
     * 测试MongoDB连接和集合
     * @return 测试结果
     */
    @CrossOrigin
    @ApiOperation(value = "测试MongoDB连接", notes = "测试MongoDB连接和集合状态")
    @RequestMapping(value = "/test-mongo-connection", method = {RequestMethod.GET})
    public ApiRest testMongoConnection() {
        try {
            String collectionName = "444d5fec7d57c38f8e270f028acfca9748d469";

            // 获取当前数据库名称
            String currentDatabase = mongoTemplate.getDb().getName();

            // 列出所有集合
            java.util.Set<String> collectionNames = mongoTemplate.getCollectionNames();

            // 检查集合是否存在
            boolean exists = mongoTemplate.collectionExists(collectionName);

            // 获取总记录数
            long count = 0;
            java.util.List<Document> docs = new java.util.ArrayList<>();

            if (exists) {
                count = mongoTemplate.count(new Query(), collectionName);

                // 获取前几条记录
                Query query = new Query();
                query.limit(3);
                docs = mongoTemplate.find(query, Document.class, collectionName);
            }

            java.util.Map<String, Object> result = new java.util.HashMap<>();
            result.put("currentDatabase", currentDatabase);
            result.put("allCollections", collectionNames);
            result.put("targetCollection", collectionName);
            result.put("collectionExists", exists);
            result.put("totalCount", count);
            result.put("sampleData", docs);

            return super.success(result);
        } catch (Exception e) {
            return super.failure("MongoDB连接测试失败: " + e.getMessage());
        }
    }

    /**
     * 用户登录
     * @return
     */
    @CrossOrigin
    @ApiOperation(value = "用户登录")
    @RequestMapping(value = "/logout", method = {RequestMethod.POST})
    public ApiRest logout(HttpServletRequest request) {
        String token = request.getHeader("token");
        System.out.println("+++++当前会话为："+token);
        baseService.logout(token);
        return super.success();
    }

    /**
     * 获取会话
     * @return
     */
    @ApiOperation(value = "获取会话")
    @RequestMapping(value = "/info", method = {RequestMethod.POST})
    public ApiRest info(@RequestParam("token") String token) {
        SysUserLoginDTO respDTO = baseService.token(token);
        return success(respDTO);
    }

    /**
     * 修改用户资料
     * @return
     */
    @ApiOperation(value = "修改用户资料")
    @RequestMapping(value = "/update", method = {RequestMethod.POST})
    public ApiRest update(@RequestBody SysUserDTO reqDTO) {
        baseService.update(reqDTO);
        return success();
    }


    /**
     * 保存或修改系统用户
     * @return
     */
    @ApiOperation(value = "保存或修改")
    @RequestMapping(value = "/save", method = {RequestMethod.POST})
    public ApiRest save(@RequestBody SysUserSaveReqDTO reqDTO) {
        baseService.save(reqDTO);
        return success();
    }


    /**
     * 批量删除
     * @param reqDTO
     * @return
     */
    @ApiOperation(value = "批量删除")
    @RequestMapping(value = "/delete", method = { RequestMethod.POST})
    public ApiRest edit(@RequestBody BaseIdsReqDTO reqDTO) {
        //根据ID删除
        baseService.removeByIds(reqDTO.getIds());
        return super.success();
    }

    /**
     * 分页查找
     * @param reqDTO
     * @return
     */
    @ApiOperation(value = "分页查找")
    @RequestMapping(value = "/paging", method = { RequestMethod.POST})
    public ApiRest<IPage<SysUserDTO>> paging(@RequestBody PagingReqDTO<SysUserDTO> reqDTO) {

        //分页查询并转换
        IPage<SysUserDTO> page = baseService.paging(reqDTO);
        return super.success(page);
    }

    /**
     * 修改状态
     * @param reqDTO
     * @return
     */
    @ApiOperation(value = "修改状态")
    @RequestMapping(value = "/state", method = { RequestMethod.POST})
    public ApiRest state(@RequestBody BaseStateReqDTO reqDTO) {

        // 条件
        QueryWrapper<SysUser> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .in(SysUser::getId, reqDTO.getIds())
                .ne(SysUser::getUserName, "admin");


        SysUser record = new SysUser();
        record.setState(reqDTO.getState());
        baseService.update(record, wrapper);

        return super.success();
    }


    /**
     * 保存或修改系统用户
     * @return
     */
    @ApiOperation(value = "学员注册")
    @RequestMapping(value = "/reg", method = {RequestMethod.POST})
    public ApiRest<SysUserLoginDTO> reg(@RequestBody SysUserDTO reqDTO) {
        SysUserLoginDTO respDTO = baseService.reg(reqDTO);
        return success(respDTO);
    }

    /**
     * 快速注册，如果手机号存在则登录，不存在就注册
     * @return
     */
    @ApiOperation(value = "快速注册")
    @RequestMapping(value = "/quick-reg", method = {RequestMethod.POST})
    public ApiRest<SysUserLoginDTO> quick(@RequestBody SysUserDTO reqDTO) {
        SysUserLoginDTO respDTO = baseService.quickReg(reqDTO);
        return success(respDTO);
    }
}
