package cn.bctools.modules.sys.user.service.impl;

import cn.bctools.common.exception.BusinessException;
import cn.bctools.modules.sys.user.dto.EpsAuthProperties;
import cn.bctools.modules.sys.user.dto.EpsValidateCodeProperties;
import cn.bctools.modules.sys.user.service.CaptchaService;
import cn.bctools.modules.sys.user.service.RedisService;
import com.wf.captcha.GifCaptcha;
import com.wf.captcha.SpecCaptcha;
import com.wf.captcha.base.Captcha;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Service
public class CaptchaServiceImpl implements CaptchaService {
    @Autowired
    private EpsAuthProperties properties;
    @Autowired
    private RedisService redisService;
    @Override
    public void generateCaptcha(HttpServletRequest request, HttpServletResponse response) {
        String key = request.getParameter("key");
        if (StringUtils.isBlank(key)) {
            throw new BusinessException("验证码key不能为空");
        }
        EpsValidateCodeProperties code = properties.getCode();
        setHeader(response, code.getType());

        Captcha captcha = createCaptcha(code);
        redisService.set(
                "biz-captcha" + key, StringUtils.lowerCase(captcha.text()), code.getTime());
        try {
            captcha.out(response.getOutputStream());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public boolean verifyCaptcha(String key, String code) {
        Object codeInRedis= redisService.get("biz-captcha" + key);;
        String type= "图形";

        if (StringUtils.isBlank(code)) {
            throw new BusinessException("请输入"+type+"验证码");
        }
        if (codeInRedis == null) {
            throw new BusinessException(type+"验证码未获取或已过期");
        }
        if (!StringUtils.equalsIgnoreCase(code, String.valueOf(codeInRedis))) {
            throw new BusinessException(type+"验证码不正确");
        }
        return true;
    }

    @Override
    public void clearCaptcha(String sessionId) {

    }

    private Captcha createCaptcha(EpsValidateCodeProperties code) {
        Captcha captcha = null;
        if (StringUtils.equalsIgnoreCase(code.getType(), "gif")) {
            captcha = new GifCaptcha(code.getWidth(), code.getHeight(), code.getLength());
        } else {
            captcha = new SpecCaptcha(code.getWidth(), code.getHeight(), code.getLength());
        }
        captcha.setCharType(code.getCharType());
        return captcha;
    }
    private void setHeader(HttpServletResponse response, String type) {
        if (StringUtils.equalsIgnoreCase(type, "gif")) {
            response.setContentType(MediaType.IMAGE_GIF_VALUE);
        } else {
            response.setContentType(MediaType.IMAGE_PNG_VALUE);
        }
        response.setHeader(HttpHeaders.PRAGMA, "No-cache");
        response.setHeader(HttpHeaders.CACHE_CONTROL, "No-cache");
        response.setDateHeader(HttpHeaders.EXPIRES, 0L);
    }
}
