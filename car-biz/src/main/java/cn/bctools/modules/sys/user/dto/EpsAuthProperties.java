package cn.bctools.modules.sys.user.dto;

import lombok.Data;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;

/**
 * <AUTHOR>
 */
@Data
@SpringBootConfiguration
@PropertySource(value = {"classpath:biz-auth.properties"})
@ConfigurationProperties(prefix = "biz.auth")
public class EpsAuthProperties {
    /**
     * 验证码配置
     */
    private EpsValidateCodeProperties code = new EpsValidateCodeProperties();
    /**
     * JWT加签密钥
     */
    private String jwtAccessKey;
    /**
     * 是否使用 JWT令牌
     */
    private Boolean enableJwt;

    /**
     * 社交登录所使用的 Client
     */
    private String socialLoginClientId;
}
