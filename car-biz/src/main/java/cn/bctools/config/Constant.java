/**
 * Copyright (c) 2016-2019 人人开源 All rights reserved.
 * <p>
 * https://www.renren.io
 * <p>
 * 版权所有，侵权必究！
 */

package cn.bctools.config;


/**
 * 常量
 *
 * <AUTHOR>
 */
public class Constant {
    /**
     * 超级管理员ID
     */
    public static final int SUPER_ADMIN = 1;
    /**
     * 当前页码
     */
    public static final String PAGE = "page";
    /**
     * 每页显示记录数
     */
    public static final String LIMIT = "limit";
    /**
     * 排序字段
     */
    public static final String ORDER_FIELD = "sidx";
    /**
     * 排序方式
     */
    public static final String ORDER = "order";
    /**
     * 升序
     */
    public static final String ASC = "asc";

    //汇总数据表
    public static final String SUM = "1805133676745330689";
    //发车数量表
    public static final String CAR_YEAR = "car_info";
    //发车数量表
    public static final String DRIVER_NUM = "1739271877061836801";
    //入职
    public static final String ADD_DRIVER = "入职";
    //离职
    public static final String DOWN_DRIVER = "离职";
    //荣誉墙
    public static final String HONOR_DATA = "1812745672113827842";
    //投诉巴南
    public static final String COM_BA_NAN_DATA = "1789114616404512770";
    //投诉渝北
    public static final String COM_YU_BEI_DATA = "1831897726197497858";
    //投诉民鑫
    public static final String COM_MING_XIN_DATA = "1777514548028346369";
    //投诉出租车
    public static final String COM_CHU_ZU_CHE_DATA = "safe_comp_ledger";
    public static final String COM_CHU_ZU_CHE_OPEN_DATA = "1750411726303047682";
    //投诉北碚
    public static final String COM_BEI_BEI_DATA = "1777535662616055810";

    //事故巴南
    public static final String ACC_BA_NAN_DATA = "1787808977862529025";
    //事故渝北
    public static final String ACC_YU_BEI_DATA = "1798887490770178049";
    //事故民鑫
    public static final String ACC_MING_XIN_DATA = "1778660697061822465";
    //事故出租车
    public static final String ACC_CHU_ZU_CHE_DATA = "safe_accident_info";
    //事故北碚
    public static final String ACC_BEI_BEI_DATA = "1777207525726326785";
    //维修(电)月
    public static final String MONTH_DATA_DIAN = "maintenance_month";
    //维修（油车）月
    public static final String MONTH_DATA_YOU = "1733880083925438466";
    //维修（油车）季度
    public static final String QUARTER_DATA = "1733000371174903810";
    //电车万公里保养
    public static final String TEN_DATA = "1733885704695451650";

    public static final String DRIVER_INFO="driver_info";

    public static final String DRIVER_QUA_REVIEW="driver_qua_review";
    public static final String[] ARR_AGE = {
            "20以下",
            "20-30",
            "30-40",
            "40-50",
            "50-60",
            "60以上"
    };
    /**
     * 菜单类型
     *
     * <AUTHOR>
     * @email <EMAIL>
     * @date 2016年11月15日 下午1:24:29
     */
    public enum MenuType {
        /**
         * 目录
         */
        CATALOG(0),
        /**
         * 菜单
         */
        MENU(1),
        /**
         * 按钮
         */
        BUTTON(2);

        private int value;

        MenuType(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
    }

    /**
     * 定时任务状态
     *
     * <AUTHOR>
     * @email <EMAIL>
     * @date 2016年12月3日 上午12:07:22
     */
    public enum ScheduleStatus {
        /**
         * 正常
         */
        NORMAL(0),
        /**
         * 暂停
         */
        PAUSE(1);

        private int value;

        ScheduleStatus(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
    }


}
