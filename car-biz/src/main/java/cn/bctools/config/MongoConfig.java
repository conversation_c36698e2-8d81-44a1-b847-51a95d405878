package cn.bctools.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.SimpleMongoClientDatabaseFactory;

/**
 * MongoDB配置类
 */
@Configuration
public class MongoConfig {

    @Value("${spring.mongodb.database:jvs_data}")
    private String database;

    @Value("${spring.mongodb.host:localhost}")
    private String host;

    @Value("${spring.mongodb.port:27017}")
    private int port;

    @Bean
    public SimpleMongoClientDatabaseFactory mongoDbFactory() {
        String connectionString = String.format("mongodb://%s:%d/%s", host, port, database);
        System.out.println("MongoDB连接字符串: " + connectionString);
        return new SimpleMongoClientDatabaseFactory(connectionString);
    }

    @Bean
    public MongoTemplate mongoTemplate() {
        return new MongoTemplate(mongoDbFactory());
    }
}
