package cn.bctools;

import cn.bctools.core.api.utils.JsonConverter;
import cn.bctools.design.use.api.RuleApi;
import cn.bctools.log.annotation.Log;
import cn.bctools.oss.mapper.SysFileMapper;
import cn.bctools.redis.utils.RedisUtils;
import cn.hutool.core.lang.Dict;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.HashMap;
import java.util.List;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2023-12-12 10:34
 * @Version: 1.0
 */

@EnableAsync
@RestController
@RequestMapping
@Slf4j
@EnableDiscoveryClient
@EnableFeignClients
@SpringBootApplication()
@EnableWebMvc
public class BizApplication implements WebMvcConfigurer {

    RuleApi ruleApi;

    @GetMapping
    public Object indexs() {
        //调用
        return new Dict().set("area", "4444")
                .set("z", "z代码返回重庆")
                .set("test", "test");
    }

    public static void main(String[] args) throws UnknownHostException{
        ConfigurableApplicationContext application = SpringApplication.run(BizApplication.class, args);
        Environment env = application.getEnvironment();
        String ip = InetAddress.getLocalHost().getHostAddress();
        String port = env.getProperty("server.port");
        String path = env.getProperty("server.servlet.context-path");

        // 未配置默认空白
        if(path == null){
            path = "";
        }


        log.info("\n----------------------------------------------------------\n\t" +
                "云帆考试系统启动成功，访问路径如下:\n\t" +
                "本地路径: \t\thttp://localhost:" + port + path + "/\n\t" +
                "网络地址: \thttp://" + ip + ":" + port + path + "/\n\t" +
                "API文档: \t\thttp://" + ip + ":" + port + path + "/swagger-ui/\n" +
                "----------------------------------------------------------");
    }
    @Override
    public void extendMessageConverters(List<HttpMessageConverter<?>> converters) {
        //保留原有converter,把新增fastConverter插入集合头,保证优先级
        converters.add(0, JsonConverter.fastConverter());
    }

}
