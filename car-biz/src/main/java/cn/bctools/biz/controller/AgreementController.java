package cn.bctools.biz.controller;
import cn.bctools.oss.controller.FileUploadController;

import cn.bctools.auth.api.api.AuthDeptServiceApi;
import cn.bctools.auth.api.dto.SysDeptDto;
import cn.bctools.biz.dto.AgreementReq;
import cn.bctools.biz.dto.AgreementResp;
import cn.bctools.biz.service.AgreementService;
import cn.bctools.common.utils.BeanCopyUtil;
import cn.bctools.common.utils.R;
import cn.bctools.common.utils.SystemThreadLocal;
import cn.bctools.log.annotation.Log;
import cn.bctools.oss.dto.BaseFile;
import cn.bctools.oss.dto.FileNameDto;
import cn.bctools.oss.template.OssTemplate;
import com.bctools.entity.driver_qua_review;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2023-12-13 17:03
 * @Version: 1.0
 */
@Slf4j
@AllArgsConstructor
@RestController
@Api(value = "驾驶员协议", tags = "驾驶员协议")
@RequestMapping("/agreement")
public class AgreementController {


    @Resource
    AgreementService agreementService;
    @Resource
    OssTemplate ossTemplate;
    @Resource
    AuthDeptServiceApi authDeptServiceApi;
    @Resource
    FileUploadController fileUploadController;

    @Log
    @ApiOperation(value = "根据部门id获取部门驾驶员协议", notes = "根据部门id获取部门驾驶员协议")
    @PostMapping("/getAgreement")
    public R getAgreement(@RequestBody AgreementReq agreementReq) {
        if (ObjectUtils.isEmpty(agreementReq.getUserId())) {
            return R.failed(-1,"未获取到驾驶员编码！");
        }
        if (ObjectUtils.isEmpty(agreementReq.getDeptId())) {
            return R.failed(-1,"未获取到部门编码！");
        }
        List<AgreementResp> resps = agreementService.getAgreement(agreementReq);
        return R.ok(resps);
    }

    @Log
    @ApiOperation(value = "签订协议", notes = "签订协议")
    @PostMapping("/addAgreement")
    public R addAgreement(@RequestBody AgreementReq agreementReq) {
        if (ObjectUtils.isEmpty(agreementReq.getUserId())) {
            return R.failed(-1,"未获取到驾驶员编码！");
        }
        Boolean resps = agreementService.addAgreement(agreementReq);
        if (!resps) {
            return R.failed("签名存储失败！");
        }
        return R.ok(resps);
    }

    @Log
    @ApiOperation(value = "根据唯一编码获取审核驾驶员信息", notes = "根据唯一编码获取审核驾驶员信息")
    @PostMapping("/getUserByCode")
    public R getUserByCode(@RequestParam("code") String code) {
        if (ObjectUtils.isEmpty(code)) {
            return R.failed(-1,"未获取到驾驶员编码！");
        }
        driver_qua_review userByCode = agreementService.getUserByCode(code);
        if (ObjectUtils.isEmpty(userByCode)) {
            return R.failed("未查询到驾驶员信息！");
        }
        return R.ok(userByCode);
    }

    @Log
    @ApiOperation(value = "文件上传", notes = "文件上传")
    @PostMapping("/fileUpload")
    public R fileUpload(@RequestPart("file") MultipartFile file) {
        SystemThreadLocal.set("label", "默认");
        BaseFile source = this.ossTemplate.putFile("jvs-form-design", "/jvs-ui/form", file.getOriginalFilename(), file);
        FileNameDto target = (FileNameDto) BeanCopyUtil.copy(source, FileNameDto.class);
        target.setOriginalFileName(file.getOriginalFilename());
        target.setFileLink(this.ossTemplate.fileLink(target.getFileName(), target.getBucketName()));
        target.setFileSize(source.getSize());
        return R.ok(target);
    }

    @Log
    @ApiOperation(value = "获取部门", notes = "获取部门")
    @PostMapping("/getDept")
    public R getDept() {
        R run = authDeptServiceApi.getAllTree();
        List<SysDeptDto> data = (List<SysDeptDto>) run.getData();
        extracted(data);
        return run;
    }

    private void extracted(List<SysDeptDto> data) {
        for (int i = 0; i < data.size(); i++) {
            List<SysDeptDto> childList = data.get(i).getChildList();
            for (int j = 0; j < childList.size(); j++) {
                if (childList.get(j).getName().contains("车队") ||
                        childList.get(j).getName().contains("分公司") ){
                    extracted(childList);
                }else {
                    childList.remove(j);
                }
            }
        }
    }
}
