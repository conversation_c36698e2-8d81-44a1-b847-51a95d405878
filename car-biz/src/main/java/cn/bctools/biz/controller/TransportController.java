package cn.bctools.biz.controller;

import cn.bctools.biz.dto.TransportUpdateReq;
import cn.bctools.common.utils.R;
import cn.bctools.log.annotation.Log;
import com.bctools.config.CreateQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.util.ObjectUtils;
import org.bson.Document;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 运输记录控制器
 * 
 * <AUTHOR>
 * @since 2025-07-17
 */
@Slf4j
@AllArgsConstructor
@RestController
@Api(value = "运输记录", tags = "运输记录")
@RequestMapping("/transport")
public class TransportController {

    @Resource
    MongoTemplate mongoTemplate;

    /**
     * 更新运输记录指定字段
     * 
     * @param req 更新请求
     * @return 更新结果
     */
    @Log
    @ApiOperation(value = "更新运输记录", notes = "根据数据ID更新附件、出库单号、充装、出发时间、状态字段")
    @PostMapping("/updateFields")
    public R updateFields(@Valid @RequestBody TransportUpdateReq req) {
        try {
            log.info("开始更新运输记录，ID: {}", req.getId());
            
            // 验证数据ID
            if (ObjectUtils.isEmpty(req.getId())) {
                return R.failed("数据ID不能为空");
            }

            // 构建查询条件
            Query query = CreateQuery.createCriteria("id", req.getId());
            
            // 先查询当前记录的状态，用于状态控制逻辑
            Document existingRecord = mongoTemplate.findOne(query, Document.class, "1937762084133539842");
            if (existingRecord == null) {
                log.warn("运输记录不存在，ID: {}", req.getId());
                return R.failed("未找到对应的记录");
            }
            
            // 获取当前状态
            String currentZhuangTai = existingRecord.getString("zhuangTai");
            log.info("当前记录状态: {}", currentZhuangTai);
            
            // 状态控制逻辑：如果当前状态是"待调度二次审核"，则不允许再次修改
            if ("待调度二次审核".equals(currentZhuangTai)) {
                log.warn("记录已处于待调度二次审核状态，不允许重复修改，ID: {}", req.getId());
                return R.failed("该记录已提交过一次，不能重复修改");
            }
            
            // 构建更新操作
            Update update = new Update();
            
            // 只更新非空字段
            if (!ObjectUtils.isEmpty(req.getFuJian())) {
                update.set("fuJian", req.getFuJian());
            }
            
            if (!ObjectUtils.isEmpty(req.getChuKuDanHao())) {
                update.set("chuKuDanHao", req.getChuKuDanHao());
            }
            
            if (!ObjectUtils.isEmpty(req.getChongZhuang())) {
                update.set("chongZhuang", req.getChongZhuang());
            }
            
            if (!ObjectUtils.isEmpty(req.getChuFaShiJian())) {
                update.set("chuFaShiJian", req.getChuFaShiJian());
            }
            
            // 第一次提交时，自动设置状态为"待调度二次审核"
            if (ObjectUtils.isEmpty(currentZhuangTai) || !"待调度二次审核".equals(currentZhuangTai)) {
                update.set("zhuangTai", "待调度二次审核");
                log.info("设置记录状态为：待调度二次审核，ID: {}", req.getId());
            }
            
            // 更新修改时间
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            update.set("updateTime", format.format(new Date()));
            
            // 执行更新操作，使用集合名称 "1937762084133539842"
            long modifiedCount = mongoTemplate.updateFirst(query, update, "1937762084133539842").getModifiedCount();
            if (modifiedCount > 0) {
                log.info("运输记录更新成功，ID: {}, 修改记录数: {}", req.getId(), modifiedCount);
                return R.ok("更新成功");
            } else {
                log.warn("运输记录更新失败，ID: {}", req.getId());
                return R.failed("更新失败");
            }
            
        } catch (Exception e) {
            log.error("更新运输记录时发生异常，ID: {}", req.getId(), e);
            return R.failed("更新失败：" + e.getMessage());
        }
    }
}
