package cn.bctools.biz.controller;

import cn.bctools.common.utils.R;
import cn.bctools.config.PlatHttpUtil;
import cn.bctools.oss.controller.FileUploadController;
import cn.bctools.oss.dto.FileNameDto;
import cn.bctools.oss.template.OssTemplate;
import com.alibaba.fastjson.JSON;
import com.bctools.dto.IdCardUrlReq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okhttp3.RequestBody;
import org.apache.commons.io.IOUtils;
import org.json.JSONObject;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import sun.misc.BASE64Encoder;

import javax.annotation.Resource;
import java.io.*;
import java.net.URL;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;


@Slf4j
@AllArgsConstructor
@RestController
@Api(value = "ocr识别", tags = "ocr识别")
@RequestMapping("baiDu")
public class BaiduApiController {
    public static final String API_KEY = "cqV7MEZZSnx9WFXyhj1P1eKz";
    public static final String SECRET_KEY = "SwOuOVdQ9ljVgCyeDsLEFnaPIrcabggc";

    @Resource
    FileUploadController fileUploadController;

    static final OkHttpClient HTTP_CLIENT = new OkHttpClient().newBuilder().build();

    @PostMapping("idCardUrl")
    @ApiOperation(value = "身份证识别", notes = "身份证识别")
    public R idCardUrl(@org.springframework.web.bind.annotation.RequestBody IdCardUrlReq req) throws Exception {
        MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
        String s = req.getUrl().replaceAll("&amp;", "&");
        String toString = new BASE64Encoder().encode(IOUtils.toByteArray(new URL(s)));
        String encode = URLEncoder.encode(toString);
        return baseUploadOcr(mediaType,encode,"idcard");
    }

    @PostMapping("carNoUrl")
    @ApiOperation(value = "车牌识别", notes = "车牌识别")
    public R carNoUrl(@org.springframework.web.bind.annotation.RequestBody IdCardUrlReq req) throws Exception {
        MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
        String s = req.getUrl().replaceAll("&amp;", "&");
        String toString = new BASE64Encoder().encode(IOUtils.toByteArray(new URL(s)));
        String encode = URLEncoder.encode(toString);
        return baseUploadOcr(mediaType,encode,"license_plate");
    }

    @PostMapping("drivingCardUrl")
    @ApiOperation(value = "驾驶证识别", notes = "驾驶证识别")
    public R drivingCardUrl(@org.springframework.web.bind.annotation.RequestBody IdCardUrlReq req) throws Exception {
        MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
        String s = req.getUrl().replaceAll("&amp;", "&");
        String toString = new BASE64Encoder().encode(IOUtils.toByteArray(new URL(s)));
        String encode = URLEncoder.encode(toString);
        return baseUploadOcr(mediaType,encode,"driving_license");
    }


    private R baseUploadOcr(MediaType mediaType, String encode, String type) throws IOException {
        RequestBody body = RequestBody.create(mediaType, "id_card_side=front&image=" + encode);
        Request request = new Request.Builder()
                .url("https://aip.baidubce.com/rest/2.0/ocr/v1/" + type + "?access_token=" + getAccessToken())
                .method("POST", body)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .addHeader("Accept", "application/json")
                .addHeader("charset", "UTF-8")
                .build();
        Response response = HTTP_CLIENT.newCall(request).execute();
        String string = response.body().string();
        com.alibaba.fastjson.JSONObject jsonObject = JSON.parseObject(string);
        Map<String, Object> result = new HashMap<>();
        result.put("resp", jsonObject);
        return R.ok(result);
    }

    /**
     * 从用户的AK，SK生成鉴权签名（Access Token）
     *
     * @return 鉴权签名（Access Token）
     * @throws IOException IO异常
     */
    static String getAccessToken() throws IOException {
        MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
        RequestBody body = RequestBody.create(mediaType, "grant_type=client_credentials&client_id=" + API_KEY
                + "&client_secret=" + SECRET_KEY);
        Request request = new Request.Builder()
                .url("https://aip.baidubce.com/oauth/2.0/token")
                .method("POST", body)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .build();
        Response response = HTTP_CLIENT.newCall(request).execute();
        return new JSONObject(response.body().string()).getString("access_token");
    }
    @PostMapping("vehiclelicense")
    public String vehiclelicense(@org.springframework.web.bind.annotation.RequestBody IdCardUrlReq req) throws Exception{
        String s = req.getUrl().replaceAll("&amp;", "&");
        byte[] bytes = IOUtils.toByteArray(new URL(s));
        String encode = new BASE64Encoder().encode(bytes);
        Map<String, Object> params = new HashMap<>();
        params.put("img",encode);
        String parm = JSON.toJSONString(params);
        return PlatHttpUtil.sendPostParams("http://**************:7006/ocr/vehiclelicense", parm,
                null,
                "UTF-8", null);
    }
}