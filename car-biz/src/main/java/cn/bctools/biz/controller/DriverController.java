package cn.bctools.biz.controller;

import cn.bctools.biz.mapper.ModelMapper;
import cn.bctools.biz.service.AgreementService;
import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.R;
import cn.bctools.log.annotation.Log;
import cn.bctools.modules.sys.user.dto.response.SysUserLoginDTO;
import cn.bctools.modules.sys.user.entity.SysUser;
import cn.bctools.modules.sys.user.mapper.SysUserMapper;
import cn.bctools.modules.sys.user.service.SysUserService;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.bctools.config.CreateQuery;
import com.bctools.dto.driver_note;
import com.bctools.entity.driver_qua_review;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.bson.Document;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2023-12-18 9:36
 * @Version: 1.0
 */
@Slf4j
@AllArgsConstructor
@RestController
@Api(value = "驾驶员", tags = "驾驶员")
@RequestMapping("/driver")
public class DriverController {
    @Resource
    AgreementService agreementService;
    @Resource
    MongoTemplate mongoTemplate;
    @Autowired
    SysUserService sysUserService;
    @Resource
    ModelMapper modelMapper;

    @Log
    @ApiOperation(value = "获取当前登录驾驶员基本信息", notes = "获取当前登录驾驶员基本信息")
    @PostMapping("/getDriver")
    public R getDriver() {
        SysUserLoginDTO principal =  (SysUserLoginDTO)SecurityUtils.getSubject().getPrincipal();
        if (ObjectUtils.isEmpty(principal)){
            throw new BusinessException("未获取到当前登录用户!");
        }
        if (ObjectUtils.isEmpty(principal.getUserCode())){
            return null;
        }
        String userCode = String.valueOf(principal.getUserCode());
        userCode = getUserCode(userCode);
        driver_qua_review userByCode = agreementService.getUserByCode(userCode);
        return R.ok(userByCode);
    }
    @Log
    @ApiOperation(value = "获取当前登录驾驶员基本信息", notes = "获取当前登录驾驶员基本信息")
    @PostMapping("/addDriver")
    public R addDriver(@RequestBody driver_qua_review driverQuaReview) {
        SimpleDateFormat format=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SysUserLoginDTO principal =  (SysUserLoginDTO)SecurityUtils.getSubject().getPrincipal();
        if (ObjectUtils.isEmpty(driverQuaReview.getShenFenZhengHao())){
            throw new BusinessException("身份证号不能为空！");
        }
        driver_qua_review userByCode = agreementService.getUserByIdCard(driverQuaReview.getShenFenZhengHao());
        if (!ObjectUtils.isEmpty(userByCode)){
            throw new BusinessException("请勿重复提交！");
        }
        String s = String.valueOf(IdWorker.getId());
        driverQuaReview.setId(s);
        driverQuaReview.setDataId(s);
        driverQuaReview.setDelFlag(Boolean.FALSE);
        driverQuaReview.setShengChengLeiXing("1");
        driverQuaReview.setCreateTime(format.format(new Date()));
        driverQuaReview.setSuoShuFenGongSiJiCheDui(principal.getDepartId());
        driverQuaReview.setModelId(modelMapper.getModelIdByCollectionName("driver_qua_review"));
        Integer maxVipNum = sysUserService.selectMaxVipNum();
        String userCode = getUserCode(String.valueOf(maxVipNum + 1));
        driverQuaReview.setJiaShiYuanWeiYiBianMa(userCode);

        //写入驾驶员简历数据
        List<driver_note> tableForm1698741445254 = driverQuaReview.getTableForm1698741445254();
        for (driver_note driverNote : tableForm1698741445254) {
            String driverNoteId = String.valueOf(IdWorker.getId());
            driverNote.setId(driverNoteId);
            driverNote.setDataId(driverNoteId);
            driverNote.setDelFlag(Boolean.FALSE);
            driverNote.setJiaShiYuanXingMing(driverQuaReview.getXingMing());
            driverNote.setJiaShiYuanWeiYiBianMa(userCode);
            driverNote.setCreateTime(format.format(new Date()));
            driverNote.setModelId(modelMapper.getModelIdByCollectionName("driver_note"));
        }
        driverQuaReview.setTableForm1698741445254(tableForm1698741445254);
        mongoTemplate.save(driverQuaReview);
        for (driver_note driverNote : tableForm1698741445254) {
            mongoTemplate.save(driverNote);
        }
        //修改当前登录用户驾驶员唯一编码
        SysUser sysUser = sysUserService.getById(principal.getId());
        if (ObjectUtils.isEmpty(sysUser.getUserCode())){
            sysUser.setUserCode(maxVipNum+1);
            sysUserService.updateNum(maxVipNum+1);
            sysUserService.updateById(sysUser);
        }
        return R.ok(true);
    }


    @NotNull
    private String getUserCode(String userCode) {
        if (Integer.parseInt(userCode)<10){
            userCode =("HGSY0000" + userCode);
        }else if (Integer.parseInt(userCode) < 100) {
            userCode =("HGSY000" + userCode);
        } else if (Integer.parseInt(userCode) < 1000) {
            userCode =("HGSY00" + userCode);
        } else if (Integer.parseInt(userCode) < 10000) {
            userCode =("HGSY0" + userCode);
        } else{
            userCode =("HGSY" + userCode);
        }
        return userCode;
    }
}
