package cn.bctools.biz.dto;

import com.bctools.dto.FileDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * 运输记录更新请求DTO
 * 
 * <AUTHOR>
 * @since 2025-07-17
 */
@Data
@ApiModel(value = "运输记录更新请求", description = "运输记录更新请求")
public class TransportUpdateReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "数据ID", required = true)
    @NotBlank(message = "数据ID不能为空")
    private String id;

    @ApiModelProperty(value = "附件")
    private List<FileDto> fuJian;

    @ApiModelProperty(value = "出库单号")
    private String chuKuDanHao;

    @ApiModelProperty(value = "充装(吨)")
    private Integer chongZhuang;

    @ApiModelProperty(value = "出发时间")
    private String chuFaShiJian;

    @ApiModelProperty(value = "状态")
    private String zhuangTai;
}
