package cn.bctools.biz.service;

import cn.bctools.auth.api.api.AuthDeptServiceApi;
import cn.bctools.auth.api.dto.SysDeptDto;
import cn.bctools.auth.api.enums.DeptEnum;
import cn.bctools.biz.dto.AgreementReq;
import cn.bctools.biz.dto.AgreementResp;
import cn.bctools.biz.mapper.ModelMapper;
import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.R;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.bctools.config.AgreeEnum;
import com.bctools.config.CreateQuery;
import com.bctools.dto.FileDto;
import com.bctools.entity.deiver_user_agree;
import com.bctools.entity.driver_agree_sign;
import com.bctools.entity.driver_qua_review;
import org.bson.Document;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import javax.xml.crypto.Data;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2023-12-14 11:19
 * @Version: 1.0
 */
@Service
public class AgreementServiceImpl implements AgreementService{

    @Resource
    MongoTemplate mongoTemplate;
    @Resource
    AuthDeptServiceApi authDeptServiceApi;
    @Resource
    ModelMapper modelMapper;

    @Override
    public List<AgreementResp> getAgreement(AgreementReq agreementReq) {
        String branchOffice = getBranchOffice(agreementReq.getDeptId());
        List<deiver_user_agree> books = mongoTemplate.find(CreateQuery.createCriteria("suoShuFenGongSi", branchOffice), deiver_user_agree.class);
        if (ObjectUtils.isEmpty(books)){
            throw new BusinessException("当前部门未配置协议！");
        }
        //获取部门协议
        deiver_user_agree userAgree = books.get(0);
        String userId = agreementReq.getUserId();
        List<driver_agree_sign> driverAgreeSigns = mongoTemplate.find(CreateQuery.createCriteria("jiaShiYuanWeiYiBianMa", userId), driver_agree_sign.class);
        List<AgreementResp> list=new ArrayList<>();
        AgreementResp czgc=new AgreementResp();
        czgc.setXieYi(userAgree.getAnQuanCaoZuoGuiCheng());
        czgc.setLeiXin(AgreeEnum.AQCZGC.getInfo());
        AgreementResp cns=new AgreementResp();
        cns.setXieYi(userAgree.getAnQuanChengNuoShu());
        cns.setLeiXin(AgreeEnum.AQCNS.getInfo());
        AgreementResp zrz=new AgreementResp();
        zrz.setXieYi(userAgree.getAnQuanShengChanZeRenZhi());
        zrz.setLeiXin(AgreeEnum.ANSCZRZ.getInfo());
        AgreementResp aqxcyz=new AgreementResp();
        aqxcyz.setXieYi(userAgree.getAnQuanXingCheYouZhiFuWuZeRenShu());
        aqxcyz.setLeiXin(AgreeEnum.AQXCZEZ.getInfo());
        AgreementResp jsyxy=new AgreementResp();
        jsyxy.setXieYi(userAgree.getJiaShiYuanXieYi());
        jsyxy.setLeiXin(AgreeEnum.JSYXY.getInfo());
        List<FileDto> qianZi=new ArrayList<>();
        //获取驾驶员已经签字的协议
        if (!ObjectUtils.isEmpty(driverAgreeSigns)) {
            driver_agree_sign driverAgreeSign = driverAgreeSigns.get(0);
            czgc.setQianZi(driverAgreeSign.getCaoZuoGuiChengQianZi());
            cns.setQianZi(driverAgreeSign.getChengNuoShuQianZi());
            jsyxy.setQianZi(driverAgreeSign.getXieYiQianZi());
            aqxcyz.setQianZi(driverAgreeSign.getZeRenShuQianZi());
            zrz.setQianZi(driverAgreeSign.getAnQuanShengChanZeRenZhiQianZi());
        }else {
            czgc.setQianZi(qianZi);
            cns.setQianZi(qianZi);
            jsyxy.setQianZi(qianZi);
            aqxcyz.setQianZi(qianZi);
            zrz.setQianZi(qianZi);
        }
        list.add(jsyxy);
        list.add(aqxcyz);
        list.add(cns);
        list.add(czgc);
        list.add(zrz);
        return list;
    }

    private String getBranchOffice(String deptId) {
        SysDeptDto byId = authDeptServiceApi.getById(deptId).getData();
        if (ObjectUtils.isEmpty(byId)){
            throw new BusinessException("所属部门或分公司不存在");
        }
        if (!byId.getName().contains("分公司")){
            deptId = getBranchOffice(byId.getParentId());
        }
        return deptId;
    }

    @Override
    public driver_qua_review getUserByCode(String code) {
        Query jiaShiYuanWeiYiBianMa = CreateQuery.createCriteria("jiaShiYuanWeiYiBianMa", code);
        return mongoTemplate.findOne(jiaShiYuanWeiYiBianMa, driver_qua_review.class);
    }
    @Override
    public driver_qua_review getUserByIdCard(String code) {
        Query jiaShiYuanWeiYiBianMa = CreateQuery.createCriteria("shenFenZhengHao", code);
        return mongoTemplate.findOne(jiaShiYuanWeiYiBianMa, driver_qua_review.class);
    }
    @Override
    public Boolean addAgreement(AgreementReq agreementReq) {
        SimpleDateFormat format=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<AgreementResp> agreementList = agreementReq.getAgreementList();
        driver_agree_sign driverAgreeSign = mongoTemplate.findOne(CreateQuery.createCriteria("jiaShiYuanWeiYiBianMa",
                agreementReq.getUserId()),
                driver_agree_sign.class);
        if (ObjectUtils.isEmpty(driverAgreeSign)){
            driverAgreeSign=new driver_agree_sign();
        }
        //获取用户
        driver_qua_review userByCode = getUserByCode(agreementReq.getUserId());
        driverAgreeSign.setJiaShiYuanWeiYiBianMa(agreementReq.getUserId());
        driverAgreeSign.setShenFenZheng(userByCode.getShenFenZhengHao());
        driverAgreeSign.setXingMing(userByCode.getXingMing());
        for (AgreementResp resp : agreementList) {
            if (AgreeEnum.AQCZGC.getInfo().equals(resp.getLeiXin())){
                driverAgreeSign.setCaoZuoGuiChengQianZi(resp.getQianZi());
            }else if (AgreeEnum.AQCNS.getInfo().equals(resp.getLeiXin())){
                driverAgreeSign.setChengNuoShuQianZi(resp.getQianZi());
            }else if (AgreeEnum.ANSCZRZ.getInfo().equals(resp.getLeiXin())){
                driverAgreeSign.setAnQuanShengChanZeRenZhiQianZi(resp.getQianZi());
            }else if (AgreeEnum.AQXCZEZ.getInfo().equals(resp.getLeiXin())){
                driverAgreeSign.setZeRenShuQianZi(resp.getQianZi());
            }else {
                driverAgreeSign.setXieYiQianZi(resp.getQianZi());
            }
        }
        driverAgreeSign.setSuoShuFenGongSiJiCheDui(agreementReq.getDeptId());
        if (ObjectUtils.isEmpty(driverAgreeSign.getId())){

            String s = String.valueOf(IdWorker.getId());
            driverAgreeSign.setId(s);
            driverAgreeSign.setDataId(s);
            driverAgreeSign.setCreateTime(format.format(new Date()));
            driverAgreeSign.setModelId(modelMapper.getModelIdByCollectionName("driver_agree_sign"));
            driverAgreeSign.setDelFlag(Boolean.FALSE);
            mongoTemplate.save(driverAgreeSign);
        }else {
            driverAgreeSign.setUpdateTime(format.format(new Date()));
            Document document = (Document) mongoTemplate.getConverter().convertToMongoType(driverAgreeSign);
            Update update = Update.fromDocument(document);
            Query jiaShiYuanWeiYiBianMa = CreateQuery.createCriteria("jiaShiYuanWeiYiBianMa", agreementReq.getUserId());
            mongoTemplate.updateFirst(jiaShiYuanWeiYiBianMa, update, driver_agree_sign.class);
        }
        return true;
    }
}
