package cn.bctools.biz.service;

import cn.bctools.biz.dto.AgreementReq;
import cn.bctools.biz.dto.AgreementResp;
import com.bctools.entity.driver_qua_review;

import java.util.List;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2023-12-13 17:16
 * @Version: 1.0
 */
public interface AgreementService {
    /**
     * 根据部门id获取部门驾驶员协议
     * @param agreementReq 部门id、驾驶员id
     * @return 协议
     */
    List<AgreementResp> getAgreement(AgreementReq agreementReq);

    /**
     * 根据唯一编码获取驾驶员信息
     * @param code 驾驶员唯一编码
     * @return 驾驶员信息
     */
    driver_qua_review getUserByCode(String code);
    /**
     * 根据身份证获取驾驶员信息
     * @param code 驾驶员唯一编码
     * @return 驾驶员信息
     */
    driver_qua_review getUserByIdCard(String code);
    /**
     * 驾驶员签字
     * @param agreementReq 签字列表，驾驶员唯一编码
     * @return 状态
     */
    Boolean addAgreement(AgreementReq agreementReq);
}
