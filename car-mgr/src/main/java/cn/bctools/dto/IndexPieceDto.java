package cn.bctools.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class IndexPieceDto {
    @ApiModelProperty("标题")
    String type;
    @ApiModelProperty("标题")
    String title;
    @ApiModelProperty("标题")
    Double price;
    @ApiModelProperty("标题")
    String unit;
    @ApiModelProperty("数据组")
    List<Double> data;
    @ApiModelProperty("数据组")
    Double proportion;
}
