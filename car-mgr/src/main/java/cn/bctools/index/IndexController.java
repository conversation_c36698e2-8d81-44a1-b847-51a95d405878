package cn.bctools.index;


import cn.bctools.common.entity.dto.UserDto;
import cn.bctools.common.utils.R;
import cn.bctools.dto.IndexPieceDto;
import cn.bctools.log.annotation.Log;
import cn.bctools.oauth2.utils.UserCurrentUtils;
import cn.bctools.redis.utils.RedisUtils;
import cn.hutool.core.lang.Dict;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.text.DecimalFormat;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.function.Supplier;


/**
 * 此只做示例展示 ,此接口用于首页demo
 */
@Slf4j
@AllArgsConstructor
@RestController
@Api("demo")
@RequestMapping("/index")
public class IndexController {

    RedisUtils redisUtils;

    @Log
    @ApiOperation(value = "获取统计数据", notes = "获取统计数据")
    @GetMapping("/statistics")
    public R statistics() {
        UserDto currentUser = UserCurrentUtils.getCurrentUser();
        String key = "index:statistics:" + currentUser.getTenantId() + currentUser.getId();
        Object cache = cache(() -> getStatistics(), key);
        return R.ok(cache);
    }

    private Object cache(Supplier fun, String key) {
        if (redisUtils.hasKey(key)) {
            return redisUtils.get(key);
        }
        Object o = fun.get();
        redisUtils.set(key, o, Duration.ofHours(2));
        return o;
    }

    private Object getStatistics() {
        int sum = 100;
        List<Dict> list = new ArrayList<>();
        Random random = new Random();
        int num1 = random.nextInt(sum);  // 生成第一个数字
        int num2 = random.nextInt(sum - num1);  // 生成第二个数字
        int num3 = random.nextInt(sum - num1 - num2);  // 生成第三个数字
        int num4 = random.nextInt(sum - num1 - num2 - num3);  // 生成第四个数字
        int num5 = sum - num1 - num2 - num3 - num4;  // 计算第五个数字
        list.add(Dict.create().set("type", "分类1").set("value", num1));
        list.add(Dict.create().set("type", "分类2").set("value", num2));
        list.add(Dict.create().set("type", "分类3").set("value", num3));
        list.add(Dict.create().set("type", "分类4").set("value", num4));
        list.add(Dict.create().set("type", "其它").set("value", num5));
        return list;
    }


    @Log
    @ApiOperation(value = "获取统计块数据", notes = "根据用户获取资源")
    @GetMapping
    public R index() {
        UserDto currentUser = UserCurrentUtils.getCurrentUser();
        String key = "index:piece:" + currentUser.getTenantId() + currentUser.getId();
        Object cache = cache(() -> getList(), key);
        return R.ok(cache);
    }

    private Object getList() {
        List<IndexPieceDto> o;
        o = new ArrayList<>();
        List<Double> random = random(10, 10000);
        double sum = random.stream().mapToDouble(Double::doubleValue).sum();
        String format = new DecimalFormat("#.00").format(sum);
        o.add(new IndexPieceDto().setPrice(Double.valueOf(format)).setTitle("本周销售金额(demo)").setType("销售").setUnit("万元").setData(random));
        List<Double> random1 = random(10, 100000);
        double sum1 = random.stream().mapToDouble(Double::doubleValue).sum();
        format = new DecimalFormat("#.00").format(sum1);
        o.add(new IndexPieceDto().setPrice(Double.valueOf(format)).setTitle("设备总量(demo)").setType("设备").setUnit("在线总量").setData(random1));
        o.add(new IndexPieceDto().setPrice(Double.valueOf((Math.round(Math.random() * 1000000) / 100))).setTitle("季度销售量(demo)").setType("季度").setUnit("单").setProportion(Math.round(Math.random() * 100) / 100.0));
        return o;
    }

    /**
     * 生成几个随机数
     *
     * @param size
     * @return
     */
    public static List<Double> random(int size, int len) {
        List<Double> list = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            Double randomValue = Math.round(Math.random() * len) / 100.0;
            list.add(randomValue);
        }
        return list;
    }

    @ApiOperation("测试")
    @GetMapping("/services/SSCJGJ_DB_790/request/header")
    public void SSCJGJ_DB_789(HttpServletRequest request){
        String testasdasdasd = request.getHeader("abc");
        String cccccddddd = request.getHeader("def");
        System.out.println(testasdasdasd);
        System.out.println(cccccddddd);
    }
}
