package cn.bctools;

import cn.bctools.log.annotation.Log;
import cn.bctools.oauth2.annotation.EnableJvsMgrResourceServer;
import cn.bctools.oss.mapper.SysFileMapper;
import cn.bctools.redis.utils.RedisUtils;
import cn.hutool.core.lang.Dict;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@EnableAsync
@RestController
@RequestMapping
@Slf4j
@EnableDiscoveryClient
@EnableFeignClients
@EnableWebSecurity
@EnableJvsMgrResourceServer
@SpringBootApplication
public class DemoMgrApplication {

    @Autowired
    RedisUtils redisUtils;

    @Resource
    SysFileMapper sysFileMapper;

    @GetMapping
    public Object indexs() {
        return new Dict().set("area", "4444")
                .set("z", "z代码返回重庆")
                .set("test", "test");
    }

    @Log
    @GetMapping("/mgr/jvs-demo/file")
    public Object files() {
        return new Dict().set("area", "4444")
                .set("z", "z代码返回重庆")
                .set("test", "test");
    }

    @Log
    @GetMapping("/file")
    public Object file() {
        sysFileMapper.selectById("00293dd4b4ff4cfd5c5cba6e0850710d");
        sysFileMapper.selectById("00293dd4b4ff4cfd5c5cba6e0850710d");
        sysFileMapper.selectById("00293dd4b4ff4cfd5c5cba6e0850710d");
        sysFileMapper.selectById("00293dd4b4ff4cfd5c5cba6e0850710d");
        sysFileMapper.selectById("00293dd4b4ff4cfd5c5cba6e0850710d");
        sysFileMapper.selectById("00293dd4b4ff4cfd5c5cba6e0850710d");
        sysFileMapper.selectById("00293dd4b4ff4cfd5c5cba6e0850710d");
        sysFileMapper.selectById("00293dd4b4ff4cfd5c5cba6e0850710d");
        sysFileMapper.selectById("00293dd4b4ff4cfd5c5cba6e0850710d");
        sysFileMapper.selectById("00293dd4b4ff4cfd5c5cba6e0850710d");
        return new Dict().set("area", "4444")
                .set("z", "z代码返回重庆")
                .set("test", "test");
    }

    public static void main(String[] args) {
        SpringApplication.run(DemoMgrApplication.class, args);
    }

}
