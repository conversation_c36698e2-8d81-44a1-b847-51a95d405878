package com.bctools.entity;

import com.bctools.dto.FileDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.MongoId;

import java.util.List;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2023-12-14 9:41
 * @Version: 1.0
 */
@Api(value = "驾驶员协议签订实体")
public class driver_agree_sign {
    @MongoId
    private ObjectId _id;
    @ApiModelProperty("id")
    private String id;
    @ApiModelProperty("dataId")
    private String dataId;
    @ApiModelProperty("modelId")
    private String modelId;
    @ApiModelProperty("驾驶员唯一编码")
    private String jiaShiYuanWeiYiBianMa;
    @ApiModelProperty("姓名")
    private String xingMing;
    @ApiModelProperty("身份证")
    private String shenFenZheng;
    @ApiModelProperty("协议签字")
    private List<FileDto> xieYiQianZi;
    @ApiModelProperty("责任书签字")
    private List<FileDto> zeRenShuQianZi;
    @ApiModelProperty("承诺书签字")
    private List<FileDto> chengNuoShuQianZi;
    @ApiModelProperty("操作规程")
    private List<FileDto> caoZuoGuiChengQianZi;
    @ApiModelProperty("安全生产责任制签字")
    private List<FileDto> anQuanShengChanZeRenZhiQianZi;
    @ApiModelProperty("delFlag")
    private Boolean delFlag;
    @ApiModelProperty("suoShuFenGongSiJiCheDui")
    private String suoShuFenGongSiJiCheDui;
    @ApiModelProperty("createTime")
    private String createTime;
    @ApiModelProperty("updateTime")
    private String updateTime;

    public List<FileDto> getChengNuoShuQianZi() {
        return chengNuoShuQianZi;
    }

    public void setChengNuoShuQianZi(final List<FileDto> chengNuoShuQianZi) {
        this.chengNuoShuQianZi = chengNuoShuQianZi;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getJiaShiYuanWeiYiBianMa() {
        return jiaShiYuanWeiYiBianMa;
    }

    public void setJiaShiYuanWeiYiBianMa(final String jiaShiYuanWeiYiBianMa) {
        this.jiaShiYuanWeiYiBianMa = jiaShiYuanWeiYiBianMa;
    }

    public String getXingMing() {
        return xingMing;
    }

    public void setXingMing(final String xingMing) {
        this.xingMing = xingMing;
    }

    public String getShenFenZheng() {
        return shenFenZheng;
    }

    public void setShenFenZheng(final String shenFenZheng) {
        this.shenFenZheng = shenFenZheng;
    }

    public List<FileDto> getXieYiQianZi() {
        return xieYiQianZi;
    }

    public void setXieYiQianZi(final List<FileDto> xieYiQianZi) {
        this.xieYiQianZi = xieYiQianZi;
    }

    public List<FileDto> getZeRenShuQianZi() {
        return zeRenShuQianZi;
    }

    public void setZeRenShuQianZi(final List<FileDto> zeRenShuQianZi) {
        this.zeRenShuQianZi = zeRenShuQianZi;
    }

    public List<FileDto> getCaoZuoGuiChengQianZi() {
        return caoZuoGuiChengQianZi;
    }

    public void setCaoZuoGuiChengQianZi(final List<FileDto> caoZuoGuiChengQianZi) {
        this.caoZuoGuiChengQianZi = caoZuoGuiChengQianZi;
    }

    public List<FileDto> getAnQuanShengChanZeRenZhiQianZi() {
        return anQuanShengChanZeRenZhiQianZi;
    }

    public void setAnQuanShengChanZeRenZhiQianZi(final List<FileDto> anQuanShengChanZeRenZhiQianZi) {
        this.anQuanShengChanZeRenZhiQianZi = anQuanShengChanZeRenZhiQianZi;
    }

    public String getDataId() {
        return dataId;
    }

    public void setDataId(final String dataId) {
        this.dataId = dataId;
    }

    public String getModelId() {
        return modelId;
    }

    public void setModelId(final String modelId) {
        this.modelId = modelId;
    }

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public Boolean getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Boolean delFlag) {
        this.delFlag = delFlag;
    }

    public String getSuoShuFenGongSiJiCheDui() {
        return suoShuFenGongSiJiCheDui;
    }

    public void setSuoShuFenGongSiJiCheDui(String suoShuFenGongSiJiCheDui) {
        this.suoShuFenGongSiJiCheDui = suoShuFenGongSiJiCheDui;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }
}
