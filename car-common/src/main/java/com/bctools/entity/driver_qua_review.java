/**
 * Copyright 2023 json.cn
 */
package com.bctools.entity;

import com.bctools.dto.FileDto;
import com.bctools.dto.driver_note;
import io.swagger.annotations.ApiModelProperty;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.MongoId;

import java.util.List;

public class driver_qua_review {
    @MongoId
    private ObjectId _id;
    @ApiModelProperty("dataId")
    private String dataId;
    @ApiModelProperty("modelId")
    private String modelId;
    /**
     * 姓名
     */
    private String xingMing;
    /**
     * 性别
     */
    private String xingBie;
    /**
     * 出生年月
     */
    private String chuShengNianYue;
    /**
     * 身份证号
     */
    private String shenFenZhengHao;
    /**
     * 家庭住址
     */
    private String jiaTingZhuZhi;
    /**
     * 驾驶证号
     */
    private String jiaShiZhengHao;
    /**
     * 准驾车型
     */
    private String zhunJiaCheXing;
    /**
     * 驾驶证有效期限
     */
    private String jiaShiZhengYouXiaoQiXian;
    /**
     * 驾驶证初次领证日期
     */
    private String jiaShiZhengChuCiLingZhengRiQi;
    /**
     * 从业证号
     */
    private String congYeZhengHao;
    /**
     * 从业有效期
     */
    private String congYeYouXiaoQiXian;
    /**
     * 从业初次领证日期
     */
    private String congYeZhengChuCiLingZhengRiQi;
    /**
     * 从业类别
     */
    private String congYeLeiBie;
    /**
     * 简历
     */
    private List<driver_note> tableForm1698741445254;
    /**
     * 所属分公司车队
     */
    private String suoShuFenGongSiJiCheDui;
    /**
     * 身份证正面
     */
    private List<FileDto> shenFenZhengZhengMian;
    /**
     * 身份证背面
     */
    private List<FileDto> shenFenZhengBeiMian;
    /**
     * 行业证
     */
    private List<FileDto> xingYeZheng;
    /**
     * 驾驶证
     */
    private List<FileDto> jiaShiZheng;
    /**
     * 驾驶员唯一编码
     */
    private String jiaShiYuanWeiYiBianMa;
    /**
     * id
     */
    private String id;
    private Boolean delFlag;
    @ApiModelProperty("createTime")
    private String createTime;
    @ApiModelProperty("updateTime")
    private String updateTime;
    private String ruZhiKaoShiChengJi;
    /**
     * 生成类型
     */
    private String shengChengLeiXing;

    public String getXingMing() {
        return xingMing;
    }

    public void setXingMing(final String xingMing) {
        this.xingMing = xingMing;
    }

    public String getXingBie() {
        return xingBie;
    }

    public void setXingBie(final String xingBie) {
        this.xingBie = xingBie;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getChuShengNianYue() {
        return chuShengNianYue;
    }

    public void setChuShengNianYue(final String chuShengNianYue) {
        this.chuShengNianYue = chuShengNianYue;
    }

    public String getShenFenZhengHao() {
        return shenFenZhengHao;
    }

    public void setShenFenZhengHao(final String shenFenZhengHao) {
        this.shenFenZhengHao = shenFenZhengHao;
    }

    public String getJiaTingZhuZhi() {
        return jiaTingZhuZhi;
    }

    public void setJiaTingZhuZhi(final String jiaTingZhuZhi) {
        this.jiaTingZhuZhi = jiaTingZhuZhi;
    }

    public String getJiaShiZhengHao() {
        return jiaShiZhengHao;
    }

    public void setJiaShiZhengHao(final String jiaShiZhengHao) {
        this.jiaShiZhengHao = jiaShiZhengHao;
    }

    public String getZhunJiaCheXing() {
        return zhunJiaCheXing;
    }

    public void setZhunJiaCheXing(final String zhunJiaCheXing) {
        this.zhunJiaCheXing = zhunJiaCheXing;
    }

    public String getJiaShiZhengYouXiaoQiXian() {
        return jiaShiZhengYouXiaoQiXian;
    }

    public void setJiaShiZhengYouXiaoQiXian(final String jiaShiZhengYouXiaoQiXian) {
        this.jiaShiZhengYouXiaoQiXian = jiaShiZhengYouXiaoQiXian;
    }

    public String getJiaShiZhengChuCiLingZhengRiQi() {
        return jiaShiZhengChuCiLingZhengRiQi;
    }

    public void setJiaShiZhengChuCiLingZhengRiQi(final String jiaShiZhengChuCiLingZhengRiQi) {
        this.jiaShiZhengChuCiLingZhengRiQi = jiaShiZhengChuCiLingZhengRiQi;
    }

    public String getCongYeZhengHao() {
        return congYeZhengHao;
    }

    public void setCongYeZhengHao(final String congYeZhengHao) {
        this.congYeZhengHao = congYeZhengHao;
    }

    public String getCongYeYouXiaoQiXian() {
        return congYeYouXiaoQiXian;
    }

    public void setCongYeYouXiaoQiXian(final String congYeYouXiaoQiXian) {
        this.congYeYouXiaoQiXian = congYeYouXiaoQiXian;
    }

    public String getCongYeZhengChuCiLingZhengRiQi() {
        return congYeZhengChuCiLingZhengRiQi;
    }

    public void setCongYeZhengChuCiLingZhengRiQi(final String congYeZhengChuCiLingZhengRiQi) {
        this.congYeZhengChuCiLingZhengRiQi = congYeZhengChuCiLingZhengRiQi;
    }

    public String getCongYeLeiBie() {
        return congYeLeiBie;
    }

    public void setCongYeLeiBie(final String congYeLeiBie) {
        this.congYeLeiBie = congYeLeiBie;
    }

    public List<driver_note> getTableForm1698741445254() {
        return tableForm1698741445254;
    }

    public void setTableForm1698741445254(final List<driver_note> tableForm1698741445254) {
        this.tableForm1698741445254 = tableForm1698741445254;
    }

    public String getSuoShuFenGongSiJiCheDui() {
        return suoShuFenGongSiJiCheDui;
    }

    public void setSuoShuFenGongSiJiCheDui(final String suoShuFenGongSiJiCheDui) {
        this.suoShuFenGongSiJiCheDui = suoShuFenGongSiJiCheDui;
    }

    public List<FileDto> getShenFenZhengZhengMian() {
        return shenFenZhengZhengMian;
    }

    public void setShenFenZhengZhengMian(final List<FileDto> shenFenZhengZhengMian) {
        this.shenFenZhengZhengMian = shenFenZhengZhengMian;
    }

    public List<FileDto> getShenFenZhengBeiMian() {
        return shenFenZhengBeiMian;
    }

    public void setShenFenZhengBeiMian(final List<FileDto> shenFenZhengBeiMian) {
        this.shenFenZhengBeiMian = shenFenZhengBeiMian;
    }

    public List<FileDto> getXingYeZheng() {
        return xingYeZheng;
    }

    public void setXingYeZheng(final List<FileDto> xingYeZheng) {
        this.xingYeZheng = xingYeZheng;
    }

    public List<FileDto> getJiaShiZheng() {
        return jiaShiZheng;
    }

    public void setJiaShiZheng(final List<FileDto> jiaShiZheng) {
        this.jiaShiZheng = jiaShiZheng;
    }

    public String getJiaShiYuanWeiYiBianMa() {
        return jiaShiYuanWeiYiBianMa;
    }

    public void setJiaShiYuanWeiYiBianMa(final String jiaShiYuanWeiYiBianMa) {
        this.jiaShiYuanWeiYiBianMa = jiaShiYuanWeiYiBianMa;
    }

    public String getDataId() {
        return dataId;
    }

    public void setDataId(String dataId) {
        this.dataId = dataId;
    }

    public String getModelId() {
        return modelId;
    }

    public void setModelId(String modelId) {
        this.modelId = modelId;
    }

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public void setDelFlag(Boolean delFlag) {
        this.delFlag = delFlag;
    }

    public Boolean getDelFlag() {
        return delFlag;
    }

    public String getShengChengLeiXing() {
        return shengChengLeiXing;
    }

    public void setShengChengLeiXing(String shengChengLeiXing) {
        this.shengChengLeiXing = shengChengLeiXing;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getRuZhiKaoShiChengJi() {
        return ruZhiKaoShiChengJi;
    }

    public void setRuZhiKaoShiChengJi(String ruZhiKaoShiChengJi) {
        this.ruZhiKaoShiChengJi = ruZhiKaoShiChengJi;
    }
}