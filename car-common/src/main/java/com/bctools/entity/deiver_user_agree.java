package com.bctools.entity;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.MongoId;

import java.io.Serializable;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2023-12-13 17:35
 * @Version: 1.0
 */

@Api(value = "驾驶员协议配置实体")
@Data
public class deiver_user_agree implements Serializable {
    private static final long serialVersionUID = 7523059264202861246L;

    @MongoId
    private ObjectId _id;
    @ApiModelProperty("id")
    private String id;
    @ApiModelProperty("dataId")
    private String dataId;
    @ApiModelProperty("modelId")
    private String modelId;
    @ApiModelProperty("所属分公司")
    private String suoShuFenGongSi;
    @ApiModelProperty("安全生产责任制")
    private String anQuanShengChanZeRenZhi;
    @ApiModelProperty("安全操作规程")
    private String anQuanCaoZuoGuiCheng;
    @ApiModelProperty("安全承诺书")
    private String anQuanChengNuoShu;
    @ApiModelProperty("安全行车优质服务责任书")
    private String anQuanXingCheYouZhiFuWuZeRenShu;
    @ApiModelProperty("驾驶员协议")
    private String jiaShiYuanXieYi;
}
