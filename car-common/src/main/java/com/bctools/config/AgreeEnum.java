package com.bctools.config;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: Created in 2023-12-14 15:01
 * @Version: 1.0
 */
public enum AgreeEnum {
    AQCZGC(1,"安全操作规程"),
    AQCNS(2,"安全承诺书"),
    ANSCZRZ(3,"安全生产责任制"),
    AQXCZEZ(4,"安全行车优质服务责任书"),
    JSYXY(5,"驾驶员协议");

    private final Integer code;
    private final String info;

    AgreeEnum(Integer code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public Integer getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }

}


