package com.bctools.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.MongoId;

import java.util.Date;
import java.util.List;

/**
 * @Author: Zhang<PERSON>yin
 * @Date: Created in 2023-12-18 14:52
 * @Version: 1.0
 */
@Data
public class driver_note {
    @MongoId
    private ObjectId _id;
    @ApiModelProperty("dataId")
    private String dataId;
    @ApiModelProperty("modelId")
    private String modelId;
    private List<String> qiZhiShiJian;
    private String jiaShiYuanXingMing;
    private String fuWuDanWei;
    private String jiaShiCheXing;
    private String jiaShiYuanWeiYiBianMa;
    private String id;
    private Boolean delFlag;
    @ApiModelProperty("createTime")
    private String createTime;
    @ApiModelProperty("updateTime")
    private String updateTime;
}
